{% extends "base.html" %}

{% block title %}Utilisateurs - CH9AYFA Bot Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-people"></i> Gestion des Utilisateurs</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshUsers()">
                <i class="bi bi-arrow-clockwise"></i> Actualiser
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportUsers()">
                <i class="bi bi-download"></i> Exporter
            </button>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un utilisateur..." onkeyup="filterUsers()">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="languageFilter" onchange="filterUsers()">
            <option value="">Toutes les langues</option>
            <option value="fr">Français</option>
            <option value="en">English</option>
            <option value="ar">العربية</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="statusFilter" onchange="filterUsers()">
            <option value="">Tous les statuts</option>
            <option value="active">Actif</option>
            <option value="inactive">Inactif</option>
        </select>
    </div>
</div>

<!-- Users Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ users|length }}</h5>
                <p class="card-text">Total Utilisateurs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ users|selectattr('language', 'equalto', 'fr')|list|length }}</h5>
                <p class="card-text">Français</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ users|selectattr('language', 'equalto', 'en')|list|length }}</h5>
                <p class="card-text">English</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ users|selectattr('language', 'equalto', 'ar')|list|length }}</h5>
                <p class="card-text">العربية</p>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-table"></i> Liste des Utilisateurs</h5>
    </div>
    <div class="card-body">
        {% if users %}
            <div class="table-responsive">
                <table class="table table-hover" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom d'utilisateur</th>
                            <th>Nom</th>
                            <th>Langue</th>
                            <th>Première visite</th>
                            <th>Dernière activité</th>
                            <th>Commandes</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr data-user-id="{{ user.user_id }}">
                            <td>
                                <code>{{ user.user_id }}</code>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                        <i class="bi bi-person text-white"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ user.username or 'N/A' }}</div>
                                        {% if user.first_name or user.last_name %}
                                            <small class="text-muted">{{ user.first_name }} {{ user.last_name }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ (user.first_name + ' ' + user.last_name) if user.first_name or user.last_name else 'N/A' }}</td>
                            <td>
                                {% if user.language == 'fr' %}
                                    <span class="badge bg-success">🇫🇷 Français</span>
                                {% elif user.language == 'en' %}
                                    <span class="badge bg-info">🇬🇧 English</span>
                                {% elif user.language == 'ar' %}
                                    <span class="badge bg-warning">🇸🇦 العربية</span>
                                {% else %}
                                    <span class="badge bg-secondary">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ user.first_seen.strftime('%d/%m/%Y %H:%M') if user.first_seen else 'N/A' }}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ user.last_seen.strftime('%d/%m/%Y %H:%M') if user.last_seen else 'N/A' }}
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ user.command_count or 0 }}</span>
                            </td>
                            <td>
                                {% if user.last_seen %}
                                    {% set days_since_last = ((moment() - user.last_seen).days) if moment else 0 %}
                                    {% if days_since_last <= 7 %}
                                        <span class="badge bg-success">Actif</span>
                                    {% elif days_since_last <= 30 %}
                                        <span class="badge bg-warning">Inactif</span>
                                    {% else %}
                                        <span class="badge bg-danger">Très inactif</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">Inconnu</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewUser('{{ user.user_id }}')" title="Voir détails">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="sendMessage('{{ user.user_id }}')" title="Envoyer message">
                                        <i class="bi bi-send"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="blockUser('{{ user.user_id }}')" title="Bloquer">
                                        <i class="bi bi-ban"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
                <h4 class="text-muted mt-3">Aucun utilisateur trouvé</h4>
                <p class="text-muted">Les utilisateurs apparaîtront ici une fois qu'ils auront interagi avec le bot.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-person-circle"></i> Détails de l'utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" onclick="sendMessage()">Envoyer Message</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function filterUsers() {
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    const languageFilter = document.getElementById('languageFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const table = document.getElementById('usersTable');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        
        const username = cells[1].textContent.toLowerCase();
        const name = cells[2].textContent.toLowerCase();
        const language = cells[3].textContent.toLowerCase();
        const status = cells[7].textContent.toLowerCase();
        
        const matchesSearch = username.includes(searchInput) || name.includes(searchInput);
        const matchesLanguage = !languageFilter || language.includes(languageFilter);
        const matchesStatus = !statusFilter || status.includes(statusFilter);
        
        if (matchesSearch && matchesLanguage && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

function refreshUsers() {
    location.reload();
}

function exportUsers() {
    showAlert('Fonctionnalité d\'export en cours de développement.', 'info');
}

function viewUser(userId) {
    // Show loading in modal
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    document.getElementById('userModalBody').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des détails...</p>
        </div>
    `;
    modal.show();
    
    // Simulate loading user details
    setTimeout(() => {
        document.getElementById('userModalBody').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Informations de base</h6>
                    <p><strong>ID:</strong> ${userId}</p>
                    <p><strong>Nom d'utilisateur:</strong> @username</p>
                    <p><strong>Nom:</strong> Nom Prénom</p>
                    <p><strong>Langue:</strong> Français</p>
                </div>
                <div class="col-md-6">
                    <h6>Statistiques</h6>
                    <p><strong>Première visite:</strong> 01/01/2024</p>
                    <p><strong>Dernière activité:</strong> Aujourd'hui</p>
                    <p><strong>Commandes utilisées:</strong> 25</p>
                    <p><strong>Statut:</strong> <span class="badge bg-success">Actif</span></p>
                </div>
            </div>
            <hr>
            <h6>Historique des commandes récentes</h6>
            <div class="list-group">
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">/info</h6>
                        <small>Il y a 2 heures</small>
                    </div>
                    <p class="mb-1">Consultation d'informations joueur</p>
                </div>
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">/events</h6>
                        <small>Hier</small>
                    </div>
                    <p class="mb-1">Consultation des événements</p>
                </div>
            </div>
        `;
    }, 1000);
}

function sendMessage(userId) {
    const message = prompt('Message à envoyer à l\'utilisateur:');
    if (message) {
        showAlert('Fonctionnalité d\'envoi de message en cours de développement.', 'info');
    }
}

function blockUser(userId) {
    if (confirm('Êtes-vous sûr de vouloir bloquer cet utilisateur ?')) {
        showAlert('Fonctionnalité de blocage en cours de développement.', 'info');
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}
</style>
{% endblock %}
