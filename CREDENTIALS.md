# 🔐 CH9AYFA Bot System - Identifiants par Défaut

## 🌐 Panel d'Administration

### Accès Web
- **URL** : `http://localhost:5000` (ou le port configuré dans FLASK_PORT)
- **Interface** : Bootstrap 5 responsive

### Comptes Administrateur par Défaut

#### Compte 1
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`
- **Rôle** : Administrateur principal

#### Compte 2
- **Utilisateur** : `souhail`
- **Mot de passe** : `ch9ayfa2024`
- **Rôle** : Développeur principal

## ⚠️ SÉCURITÉ IMPORTANTE

### Actions Obligatoires en Production
1. **Changez immédiatement les mots de passe par défaut**
2. **Modifiez la clé secrète Flask** dans le fichier `.env`
3. **Activez HTTPS** avec un certificat SSL
4. **Configurez un reverse proxy** (nginx recommandé)
5. **Désactivez le mode debug** (`FLASK_DEBUG=False`)

### Modification des Identifiants
Pour changer les identifiants par défaut, modifiez le fichier `admin_panel.py` :

```python
# Ligne ~40 dans admin_panel.py
ADMIN_CREDENTIALS = {
    'votre_nouveau_nom': generate_password_hash('votre_nouveau_mot_de_passe'),
    'autre_admin': generate_password_hash('autre_mot_de_passe')
}
```

## 🤖 Bot Telegram

### Configuration Requise
- **Token Bot** : Obtenu via @BotFather sur Telegram
- **Groupe Développeur** : ID du groupe où le bot peut fonctionner
- **Administrateurs** : IDs des utilisateurs autorisés

### Commandes Admin
- `/getgroupid` : Obtenir l'ID du groupe actuel (admin uniquement)

## 🔧 Configuration Recommandée

### Fichier .env Minimal
```env
TELEGRAM_BOT_TOKEN=votre_token_ici
DEVELOPER_GROUP_ID=-1001234567890
ADMIN_USER_IDS=123456789,987654321
FLASK_SECRET_KEY=cle-secrete-unique-et-complexe
FLASK_PORT=5000
FLASK_DEBUG=False
```

### Permissions Telegram
1. **Bot dans le groupe** : Le bot doit être membre du groupe développeur
2. **Droits admin** : Recommandé pour certaines fonctionnalités
3. **Messages privés** : Le bot peut recevoir des messages privés des admins

## 🛡️ Bonnes Pratiques de Sécurité

### Mots de Passe
- Minimum 12 caractères
- Mélange de lettres, chiffres et symboles
- Unique pour chaque compte
- Changement régulier

### Accès Réseau
- Utilisez un firewall
- Limitez l'accès au port Flask
- Surveillez les tentatives de connexion
- Activez les logs de sécurité

### Sauvegarde
- Sauvegardez régulièrement la configuration
- Chiffrez les sauvegardes
- Testez la restauration
- Documentez les procédures

## 📊 Monitoring

### Logs à Surveiller
- Tentatives de connexion échouées
- Commandes bot exécutées
- Erreurs système
- Utilisation des ressources

### Alertes Recommandées
- Connexions suspectes
- Erreurs répétées
- Utilisation anormale
- Pannes de service

## 🆘 En Cas de Problème

### Connexion Impossible
1. Vérifiez les identifiants
2. Consultez les logs Flask
3. Testez avec `python test_system.py`
4. Redémarrez le service

### Bot Non Responsive
1. Vérifiez le token Telegram
2. Confirmez l'ID du groupe
3. Vérifiez les permissions
4. Consultez les logs du bot

### Contact Support
- Consultez d'abord cette documentation
- Vérifiez les logs d'erreur
- Préparez les informations système
- Contactez l'équipe de développement

---

**🔒 Gardez ces informations confidentielles et sécurisées !**
