#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Firebase Database Manager for Telegram Bot
Handles user preferences, language settings, and bot data
"""

import os
import json
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FirebaseManager:
    """Firebase Firestore database manager with local fallback"""

    def __init__(self, service_account_path: str = "souhail-a101a-firebase-adminsdk-fbsvc-fc33476ebd.json"):
        """
        Initialize Firebase connection

        Args:
            service_account_path: Path to Firebase service account JSON file
        """
        self.db = None
        self.service_account_path = service_account_path
        self.firebase_enabled = False

        # Local storage fallback
        self.local_users = {}
        self.local_commands = []

        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK with fallback to local storage"""
        try:
            # Check if Firebase is already initialized
            if not firebase_admin._apps:
                # Initialize Firebase with service account
                if os.path.exists(self.service_account_path):
                    cred = credentials.Certificate(self.service_account_path)
                    firebase_admin.initialize_app(cred)
                    logger.info("✅ Firebase initialized successfully")
                else:
                    logger.warning(f"⚠️ Service account file not found: {self.service_account_path}")
                    logger.info("📁 Using local storage fallback")
                    return

            # Get Firestore client
            self.db = firestore.client()

            # Test connection
            test_ref = self.db.collection('test').document('connection_test')
            test_ref.set({'test': True})
            test_ref.delete()

            self.firebase_enabled = True
            logger.info("✅ Firestore client connected and tested")

        except Exception as e:
            logger.warning(f"⚠️ Firebase initialization failed: {e}")
            logger.info("📁 Falling back to local storage")
            self.firebase_enabled = False
    
    def set_user_language(self, user_id: int, language: str) -> bool:
        """
        Set user's preferred language

        Args:
            user_id: Telegram user ID
            language: Language code (en, fr, ar)

        Returns:
            bool: Success status
        """
        if self.firebase_enabled:
            try:
                user_ref = self.db.collection('users').document(str(user_id))
                user_ref.set({
                    'language': language,
                    'updated_at': datetime.now(),
                    'user_id': user_id
                }, merge=True)

                logger.info(f"✅ Language set to {language} for user {user_id} (Firebase)")
                return True

            except Exception as e:
                logger.error(f"❌ Failed to set language for user {user_id}: {e}")
                # Fall back to local storage
                self.firebase_enabled = False

        # Local storage fallback
        if str(user_id) not in self.local_users:
            self.local_users[str(user_id)] = {}

        self.local_users[str(user_id)]['language'] = language
        self.local_users[str(user_id)]['updated_at'] = datetime.now()

        logger.info(f"✅ Language set to {language} for user {user_id} (Local)")
        return True
    
    def get_user_language(self, user_id: int) -> str:
        """
        Get user's preferred language

        Args:
            user_id: Telegram user ID

        Returns:
            str: Language code (default: 'en')
        """
        if self.firebase_enabled:
            try:
                user_ref = self.db.collection('users').document(str(user_id))
                doc = user_ref.get()

                if doc.exists:
                    data = doc.to_dict()
                    language = data.get('language', 'en')
                    logger.info(f"✅ Retrieved language {language} for user {user_id} (Firebase)")
                    return language
                else:
                    logger.info(f"ℹ️ No language preference found for user {user_id}, using default 'en'")
                    return 'en'

            except Exception as e:
                logger.error(f"❌ Failed to get language for user {user_id}: {e}")
                # Fall back to local storage
                self.firebase_enabled = False

        # Local storage fallback
        user_data = self.local_users.get(str(user_id), {})
        language = user_data.get('language', 'en')
        logger.info(f"✅ Retrieved language {language} for user {user_id} (Local)")
        return language
    
    def save_user_info(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """
        Save user information to database
        
        Args:
            user_id: Telegram user ID
            user_data: Dictionary containing user information
            
        Returns:
            bool: Success status
        """
        try:
            user_ref = self.db.collection('users').document(str(user_id))
            
            # Add metadata
            user_data.update({
                'user_id': user_id,
                'last_seen': datetime.now(),
                'updated_at': datetime.now()
            })
            
            user_ref.set(user_data, merge=True)
            logger.info(f"✅ User info saved for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save user info for user {user_id}: {e}")
            return False
    
    def get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user information from database
        
        Args:
            user_id: Telegram user ID
            
        Returns:
            Optional[Dict]: User data or None if not found
        """
        try:
            user_ref = self.db.collection('users').document(str(user_id))
            doc = user_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                logger.info(f"✅ Retrieved user info for user {user_id}")
                return data
            else:
                logger.info(f"ℹ️ No user info found for user {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get user info for user {user_id}: {e}")
            return None
    
    def log_command_usage(self, user_id: int, command: str, success: bool = True) -> bool:
        """
        Log command usage for analytics
        
        Args:
            user_id: Telegram user ID
            command: Command name
            success: Whether command was successful
            
        Returns:
            bool: Success status
        """
        try:
            log_ref = self.db.collection('command_logs').document()
            log_ref.set({
                'user_id': user_id,
                'command': command,
                'success': success,
                'timestamp': datetime.now()
            })
            
            logger.info(f"✅ Command usage logged: {command} by user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to log command usage: {e}")
            return False
    
    def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """
        Get user statistics
        
        Args:
            user_id: Telegram user ID
            
        Returns:
            Dict: User statistics
        """
        try:
            # Get command usage count
            logs_ref = self.db.collection('command_logs').where('user_id', '==', user_id)
            logs = logs_ref.get()
            
            command_count = {}
            total_commands = 0
            
            for log in logs:
                data = log.to_dict()
                command = data.get('command', 'unknown')
                command_count[command] = command_count.get(command, 0) + 1
                total_commands += 1
            
            stats = {
                'total_commands': total_commands,
                'command_breakdown': command_count,
                'user_id': user_id
            }
            
            logger.info(f"✅ Retrieved stats for user {user_id}")
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get user stats for user {user_id}: {e}")
            return {'total_commands': 0, 'command_breakdown': {}, 'user_id': user_id}
    
    def get_all_users_count(self) -> int:
        """
        Get total number of users

        Returns:
            int: Total user count
        """
        try:
            users_ref = self.db.collection('users')
            users = users_ref.get()
            count = len(users)

            logger.info(f"✅ Total users count: {count}")
            return count

        except Exception as e:
            logger.error(f"❌ Failed to get users count: {e}")
            return 0

    def get_all_users(self) -> list:
        """
        Get all users for admin panel

        Returns:
            list: List of all users with their data
        """
        if self.firebase_enabled:
            try:
                users_ref = self.db.collection('users')
                users = users_ref.get()

                users_list = []
                for user in users:
                    user_data = user.to_dict()
                    user_data['user_id'] = user.id

                    # Get command count for this user
                    logs_ref = self.db.collection('command_logs').where('user_id', '==', int(user.id))
                    logs = logs_ref.get()
                    user_data['command_count'] = len(logs)

                    # Set first_seen if not exists
                    if 'first_seen' not in user_data and 'updated_at' in user_data:
                        user_data['first_seen'] = user_data['updated_at']

                    users_list.append(user_data)

                logger.info(f"✅ Retrieved {len(users_list)} users")
                return users_list

            except Exception as e:
                logger.error(f"❌ Failed to get all users: {e}")
                return []

        # Local storage fallback
        users_list = []
        for user_id, user_data in self.local_users.items():
            user_data['user_id'] = user_id
            user_data['command_count'] = 0  # No command tracking in local mode
            users_list.append(user_data)

        return users_list

    def get_total_users(self) -> int:
        """Get total number of users"""
        return len(self.get_all_users())

    def get_total_commands(self) -> int:
        """
        Get total number of commands executed

        Returns:
            int: Total command count
        """
        if self.firebase_enabled:
            try:
                logs_ref = self.db.collection('command_logs')
                logs = logs_ref.get()
                count = len(logs)

                logger.info(f"✅ Total commands count: {count}")
                return count

            except Exception as e:
                logger.error(f"❌ Failed to get commands count: {e}")
                return 0

        # Local storage fallback
        return len(self.local_commands)

    def get_recent_activity(self, limit: int = 10) -> list:
        """
        Get recent command activity

        Args:
            limit: Maximum number of activities to return

        Returns:
            list: Recent activities
        """
        if self.firebase_enabled:
            try:
                logs_ref = self.db.collection('command_logs').order_by('timestamp', direction=firestore.Query.DESCENDING).limit(limit)
                logs = logs_ref.get()

                activities = []
                for log in logs:
                    log_data = log.to_dict()

                    # Get user info
                    user_id = log_data.get('user_id')
                    user_ref = self.db.collection('users').document(str(user_id))
                    user_doc = user_ref.get()

                    if user_doc.exists:
                        user_data = user_doc.to_dict()
                        log_data['username'] = user_data.get('username', 'Unknown')
                        log_data['first_name'] = user_data.get('first_name', '')
                    else:
                        log_data['username'] = 'Unknown'
                        log_data['first_name'] = ''

                    activities.append(log_data)

                logger.info(f"✅ Retrieved {len(activities)} recent activities")
                return activities

            except Exception as e:
                logger.error(f"❌ Failed to get recent activity: {e}")
                return []

        # Local storage fallback
        return self.local_commands[-limit:] if self.local_commands else []

    def get_command_statistics(self) -> list:
        """
        Get command usage statistics

        Returns:
            list: Command statistics
        """
        if self.firebase_enabled:
            try:
                logs_ref = self.db.collection('command_logs')
                logs = logs_ref.get()

                command_stats = {}

                for log in logs:
                    log_data = log.to_dict()
                    command = log_data.get('command', 'unknown')
                    success = log_data.get('success', True)
                    timestamp = log_data.get('timestamp')

                    if command not in command_stats:
                        command_stats[command] = {
                            'name': command,
                            'count': 0,
                            'success_count': 0,
                            'error_count': 0,
                            'last_used': None
                        }

                    command_stats[command]['count'] += 1

                    if success:
                        command_stats[command]['success_count'] += 1
                    else:
                        command_stats[command]['error_count'] += 1

                    if not command_stats[command]['last_used'] or timestamp > command_stats[command]['last_used']:
                        command_stats[command]['last_used'] = timestamp

                # Convert to list and sort by usage
                stats_list = list(command_stats.values())
                stats_list.sort(key=lambda x: x['count'], reverse=True)

                logger.info(f"✅ Retrieved command statistics for {len(stats_list)} commands")
                return stats_list

            except Exception as e:
                logger.error(f"❌ Failed to get command statistics: {e}")
                return []

        # Local storage fallback
        return []

# Global Firebase manager instance
firebase_manager = None

def get_firebase_manager() -> FirebaseManager:
    """Get or create Firebase manager instance"""
    global firebase_manager
    if firebase_manager is None:
        firebase_manager = FirebaseManager()
    return firebase_manager

def init_firebase_db():
    """Initialize Firebase database connection"""
    try:
        global firebase_manager
        firebase_manager = FirebaseManager()
        logger.info("🔥 Firebase database initialized successfully!")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to initialize Firebase: {e}")
        return False

if __name__ == "__main__":
    # Test Firebase connection
    print("🔥 Testing Firebase connection...")
    
    try:
        fb = FirebaseManager()
        print("✅ Firebase connection successful!")
        
        # Test operations
        test_user_id = 12345
        
        # Test language setting
        fb.set_user_language(test_user_id, 'fr')
        lang = fb.get_user_language(test_user_id)
        print(f"✅ Language test: {lang}")
        
        # Test user info
        fb.save_user_info(test_user_id, {
            'username': 'test_user',
            'first_name': 'Test',
            'last_name': 'User'
        })
        
        user_info = fb.get_user_info(test_user_id)
        print(f"✅ User info test: {user_info}")
        
        # Test command logging
        fb.log_command_usage(test_user_id, '/start')
        fb.log_command_usage(test_user_id, '/help')
        
        stats = fb.get_user_stats(test_user_id)
        print(f"✅ Stats test: {stats}")
        
        print("🎉 All Firebase tests passed!")
        
    except Exception as e:
        print(f"❌ Firebase test failed: {e}")
        import traceback
        traceback.print_exc()
