# Test script to verify language functionality
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# User language storage (in production, use a database)
user_languages = {}

# Language translations
LANGUAGES = {
    'fr': {
        'welcome_title': '🔥 CH9AYFA API BOT 🔥',
        'welcome_msg': '🎮 **Bienvenue dans le bot ultime Free Fire !**',
        'commands_title': '📋 COMMANDES',
        'quick_start': '💡 **Démarrage rapide :** Essayez `/info 2511293320`',
        'help_center': '📚 CENTRE D\'AIDE',
        'detailed_guide': '🎯 **GUIDE DÉTAILLÉ DES COMMANDES**',
        'player_info': '🔍 INFORMATIONS JOUEUR',
        'status_check': '✅ VÉRIFICATION STATUT',
        'game_events': '📅 ÉVÉNEMENTS JEU',
        'spam_service': '📧 SERVICE SPAM',
        'likes_service': '❤️ SERVICE LIKES',
        'language_service': '🌐 LANGUE',
        'need_help': '🔥 **Besoin d\'aide ?** Contactez le support !',
        'unknown_command': '🚫 **COMMANDE INCONNUE**',
        'dont_recognize': '❓ Je ne reconnais pas cette commande !',
        'available_commands': '💡 **Commandes disponibles :**',
        'type_help': '🔥 Tapez `/help` pour des instructions détaillées !',
        'select_language': '🌐 **Sélectionnez votre langue**',
        'language_changed': '✅ **Langue changée en Français !**',
    },
    'en': {
        'welcome_title': '🔥 CH9AYFA API BOT 🔥',
        'welcome_msg': '🎮 **Welcome to the ultimate Free Fire info bot!**',
        'commands_title': '📋 COMMANDS',
        'quick_start': '💡 **Quick Start:** Try `/info 2511293320`',
        'help_center': '📚 HELP CENTER',
        'detailed_guide': '🎯 **DETAILED COMMAND GUIDE**',
        'player_info': '🔍 PLAYER INFO',
        'status_check': '✅ STATUS CHECK',
        'game_events': '📅 GAME EVENTS',
        'spam_service': '📧 SPAM SERVICE',
        'likes_service': '❤️ LIKES SERVICE',
        'language_service': '🌐 LANGUAGE',
        'need_help': '🔥 **Need more help?** Contact support!',
        'unknown_command': '🚫 **UNKNOWN COMMAND**',
        'dont_recognize': '❓ I don\'t recognize that command!',
        'available_commands': '💡 **Available Commands:**',
        'type_help': '🔥 Type `/help` for detailed instructions!',
        'select_language': '🌐 **Select your language**',
        'language_changed': '✅ **Language changed to English!**',
    },
    'ar': {
        'welcome_title': '🔥 بوت CH9AYFA API 🔥',
        'welcome_msg': '🎮 **مرحباً بك في بوت فري فاير المتقدم!**',
        'commands_title': '📋 الأوامر',
        'quick_start': '💡 **البداية السريعة:** جرب `/info 2511293320`',
        'help_center': '📚 مركز المساعدة',
        'detailed_guide': '🎯 **دليل الأوامر المفصل**',
        'player_info': '🔍 معلومات اللاعب',
        'status_check': '✅ فحص الحالة',
        'game_events': '📅 أحداث اللعبة',
        'spam_service': '📧 خدمة الرسائل',
        'likes_service': '❤️ خدمة الإعجابات',
        'language_service': '🌐 اللغة',
        'need_help': '🔥 **تحتاج مساعدة أكثر؟** اتصل بالدعم!',
        'unknown_command': '🚫 **أمر غير معروف**',
        'dont_recognize': '❓ لا أتعرف على هذا الأمر!',
        'available_commands': '💡 **الأوامر المتاحة:**',
        'type_help': '🔥 اكتب `/help` للحصول على تعليمات مفصلة!',
        'select_language': '🌐 **اختر لغتك**',
        'language_changed': '✅ **تم تغيير اللغة إلى العربية!**',
    }
}

def get_user_language(user_id):
    """Get user's preferred language, default to English"""
    return user_languages.get(user_id, 'en')

def get_text(user_id, key):
    """Get translated text for user"""
    lang = get_user_language(user_id)
    return LANGUAGES[lang].get(key, LANGUAGES['en'].get(key, key))

# Test the functions
def test_language_system():
    print("Testing language system...")
    
    # Test English (default)
    user_id = 123
    print(f"English: {get_text(user_id, 'welcome_msg')}")
    
    # Test French
    user_languages[user_id] = 'fr'
    print(f"French: {get_text(user_id, 'welcome_msg')}")
    
    # Test Arabic
    user_languages[user_id] = 'ar'
    print(f"Arabic: {get_text(user_id, 'welcome_msg')}")
    
    print("Language system working correctly!")

if __name__ == "__main__":
    test_language_system()
