{% extends "base.html" %}

{% block title %}Tableau de bord - CH9AYFA Bot Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-speedometer2"></i> Tableau de bord</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                <i class="bi bi-arrow-clockwise"></i> Actualiser
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Utilisateurs Total</h6>
                    <h3 class="mb-0" id="total-users">{{ stats.total_users or 0 }}</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-people" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card success">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Commandes Total</h6>
                    <h3 class="mb-0" id="total-commands">{{ stats.total_commands or 0 }}</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-terminal" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card warning">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Statut Bot</h6>
                    <h3 class="mb-0" id="bot-status">{{ stats.bot_status or 'Offline' }}</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-robot" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card info">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Admins</h6>
                    <h3 class="mb-0" id="admin-count">{{ stats.admin_count or 0 }}</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-shield-check" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and Bot Info -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-activity"></i> Activité Récente</h5>
            </div>
            <div class="card-body">
                {% if stats.recent_activity %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Commande</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in stats.recent_activity %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="bi bi-person text-white"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ activity.username or 'Utilisateur' }}</div>
                                                <small class="text-muted">ID: {{ activity.user_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ activity.command }}</span>
                                    </td>
                                    <td>
                                        {% if activity.success %}
                                            <span class="badge bg-success">Succès</span>
                                        {% else %}
                                            <span class="badge bg-danger">Échec</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ activity.timestamp.strftime('%d/%m/%Y %H:%M') if activity.timestamp else 'N/A' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem; color: #dee2e6;"></i>
                        <p class="text-muted mt-2">Aucune activité récente</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations Bot</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">Groupe Développeur</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-people"></i></span>
                        <input type="text" class="form-control" value="{{ stats.developer_group_id }}" readonly>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Statut du Bot</label>
                    <div class="d-flex align-items-center">
                        <div class="status-indicator bg-success rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                        <span class="text-success fw-bold">{{ stats.bot_status }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label fw-bold">Dernière Mise à Jour</label>
                    <p class="text-muted mb-0" id="last-update">{{ moment().strftime('%d/%m/%Y %H:%M') if moment else 'N/A' }}</p>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('users') }}" class="btn btn-outline-primary">
                        <i class="bi bi-people"></i> Gérer Utilisateurs
                    </a>
                    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-gear"></i> Paramètres
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Actions Rapides</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-sm" onclick="sendTestMessage()">
                        <i class="bi bi-send"></i> Test Message Bot
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="clearLogs()">
                        <i class="bi bi-trash"></i> Vider Logs
                    </button>
                    <button class="btn btn-info btn-sm" onclick="exportData()">
                        <i class="bi bi-download"></i> Exporter Données
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshStats() {
    // Show loading state
    const refreshBtn = document.querySelector('[onclick="refreshStats()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Chargement...';
    refreshBtn.disabled = true;
    
    // Fetch updated stats
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // Update stats
            document.getElementById('total-users').textContent = data.total_users || 0;
            document.getElementById('total-commands').textContent = data.total_commands || 0;
            document.getElementById('bot-status').textContent = data.bot_status || 'Offline';
            document.getElementById('admin-count').textContent = data.admin_count || 0;
            
            // Update last update time
            document.getElementById('last-update').textContent = new Date().toLocaleString('fr-FR');
            
            // Show success message
            showAlert('Statistiques mises à jour avec succès!', 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Erreur lors de la mise à jour des statistiques.', 'danger');
        })
        .finally(() => {
            // Restore button
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        });
}

function sendTestMessage() {
    showAlert('Fonctionnalité en cours de développement.', 'info');
}

function clearLogs() {
    if (confirm('Êtes-vous sûr de vouloir vider tous les logs ?')) {
        showAlert('Fonctionnalité en cours de développement.', 'info');
    }
}

function exportData() {
    showAlert('Fonctionnalité en cours de développement.', 'info');
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-refresh stats every 30 seconds
setInterval(refreshStats, 30000);
</script>

<style>
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

.status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>
{% endblock %}
