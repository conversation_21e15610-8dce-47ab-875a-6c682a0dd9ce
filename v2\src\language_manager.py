#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CH9AYFA Bot System v2.0 - Language Manager
Enhanced multilingual support with dynamic loading and caching
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class LanguageManager:
    """Enhanced language manager with caching and fallback support"""
    
    def __init__(self, languages_dir: str = None):
        self.languages_dir = Path(languages_dir) if languages_dir else Path(__file__).parent.parent / "config" / "languages"
        self.languages: Dict[str, Dict[str, Any]] = {}
        self.default_language = 'en'
        self.supported_languages = ['en', 'fr', 'ar']
        
        # Create languages directory if it doesn't exist
        self.languages_dir.mkdir(parents=True, exist_ok=True)
        
        # Load all languages
        self._load_languages()
        
        logger.info(f"✅ Language Manager initialized with {len(self.languages)} languages")
    
    def _load_languages(self):
        """Load all language files"""
        # Create default language files if they don't exist
        self._create_default_language_files()
        
        # Load language files
        for lang_code in self.supported_languages:
            lang_file = self.languages_dir / f"{lang_code}.json"
            
            if lang_file.exists():
                try:
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        self.languages[lang_code] = json.load(f)
                    logger.debug(f"Loaded language: {lang_code}")
                except Exception as e:
                    logger.error(f"Failed to load language {lang_code}: {e}")
                    self.languages[lang_code] = self._get_default_texts()
            else:
                logger.warning(f"Language file not found: {lang_file}")
                self.languages[lang_code] = self._get_default_texts()
    
    def _create_default_language_files(self):
        """Create default language files if they don't exist"""
        
        # English (default)
        en_texts = {
            "welcome_message": """
🔥 **Welcome to CH9AYFA Bot v2.0!**

I'm your advanced Free Fire assistant with enhanced features and improved performance.

🚀 **New in v2.0:**
• Faster response times
• Enhanced security
• Better error handling
• Real-time analytics
• Improved caching

📋 **Available Commands:**
• `/help` - Detailed help guide
• `/info <uid>` - Get player information
• `/check <uid>` - Check player status
• `/events [region]` - View game events
• `/spam <uid>` - Send spam service
• `/likes <uid>` - Send likes service
• `/language` - Change language
• `/ping` - Test bot response

💡 **Tip:** Use `/help` for detailed command explanations.

🔒 **Security:** This bot is restricted to authorized users only.
            """,
            "help_message": """
📚 **CH9AYFA Bot v2.0 - Help Guide**

🔍 **Player Commands:**
• `/info <uid>` - Get detailed player information
  Example: `/info 2511293320`

• `/check <uid>` - Check player status
  Example: `/check 123456789`

📅 **Events:**
• `/events` - Get events for Middle East region
• `/events <region>` - Get events for specific region
  Example: `/events NA`

📧 **Services:**
• `/spam <uid>` - Send spam to player
• `/likes <uid>` - Send likes to player

🌐 **Settings:**
• `/language` - Change bot language
• `/ping` - Test bot response time

👨‍💼 **Admin Commands:**
• `/getgroupid` - Get current group ID
• `/stats` - View bot statistics

❓ **Need Help?**
Contact the development team for support.

🔥 **CH9AYFA Bot v2.0** - Enhanced Free Fire Assistant
            """,
            "unknown_command": "❌ Unknown command. Use `/help` to see available commands.",
            "unauthorized_access": "❌ Access denied. This bot is restricted to authorized users only.",
            "error_occurred": "❌ An error occurred. Please try again later.",
            "loading": "⏳ Loading...",
            "success": "✅ Success!",
            "failed": "❌ Failed!",
            "player_not_found": "❌ Player not found.",
            "invalid_uid": "❌ Invalid UID format.",
            "rate_limit_exceeded": "⚠️ Rate limit exceeded. Please wait before sending another command.",
            "command_disabled": "❌ This command is currently disabled.",
            "admin_only": "❌ This command is reserved for administrators.",
            "language_changed": "✅ Language changed successfully!",
            "choose_language": "🌐 Choose your language:",
            "commands_list": """
📋 **Available Commands:**

▸ `/start` - Welcome message
▸ `/help` - Detailed help
▸ `/info <uid>` - Player information
▸ `/check <uid>` - Status check
▸ `/events [region]` - Game events
▸ `/spam <uid>` - Spam service
▸ `/likes <uid>` - Likes service
▸ `/language` - Change language
▸ `/ping` - Test response
▸ `/getgroupid` - Get group ID (Admin)
▸ `/stats` - Bot statistics (Admin)
            """
        }
        
        # French
        fr_texts = {
            "welcome_message": """
🔥 **Bienvenue sur CH9AYFA Bot v2.0 !**

Je suis votre assistant Free Fire avancé avec des fonctionnalités améliorées et de meilleures performances.

🚀 **Nouveautés v2.0 :**
• Temps de réponse plus rapides
• Sécurité renforcée
• Meilleure gestion d'erreurs
• Analyses en temps réel
• Cache amélioré

📋 **Commandes disponibles :**
• `/help` - Guide d'aide détaillé
• `/info <uid>` - Informations joueur
• `/check <uid>` - Vérifier statut joueur
• `/events [région]` - Voir événements
• `/spam <uid>` - Service spam
• `/likes <uid>` - Service likes
• `/language` - Changer langue
• `/ping` - Tester réponse bot

💡 **Astuce :** Utilisez `/help` pour des explications détaillées.

🔒 **Sécurité :** Ce bot est réservé aux utilisateurs autorisés.
            """,
            "help_message": """
📚 **CH9AYFA Bot v2.0 - Guide d'Aide**

🔍 **Commandes Joueur :**
• `/info <uid>` - Informations détaillées du joueur
  Exemple : `/info 2511293320`

• `/check <uid>` - Vérifier le statut du joueur
  Exemple : `/check 123456789`

📅 **Événements :**
• `/events` - Événements région Moyen-Orient
• `/events <région>` - Événements région spécifique
  Exemple : `/events NA`

📧 **Services :**
• `/spam <uid>` - Envoyer spam au joueur
• `/likes <uid>` - Envoyer likes au joueur

🌐 **Paramètres :**
• `/language` - Changer langue du bot
• `/ping` - Tester temps de réponse

👨‍💼 **Commandes Admin :**
• `/getgroupid` - Obtenir ID du groupe
• `/stats` - Voir statistiques bot

❓ **Besoin d'aide ?**
Contactez l'équipe de développement.

🔥 **CH9AYFA Bot v2.0** - Assistant Free Fire Amélioré
            """,
            "unknown_command": "❌ Commande inconnue. Utilisez `/help` pour voir les commandes disponibles.",
            "unauthorized_access": "❌ Accès refusé. Ce bot est réservé aux utilisateurs autorisés.",
            "error_occurred": "❌ Une erreur s'est produite. Veuillez réessayer plus tard.",
            "loading": "⏳ Chargement...",
            "success": "✅ Succès !",
            "failed": "❌ Échec !",
            "player_not_found": "❌ Joueur introuvable.",
            "invalid_uid": "❌ Format UID invalide.",
            "rate_limit_exceeded": "⚠️ Limite de taux dépassée. Veuillez attendre avant d'envoyer une autre commande.",
            "command_disabled": "❌ Cette commande est actuellement désactivée.",
            "admin_only": "❌ Cette commande est réservée aux administrateurs.",
            "language_changed": "✅ Langue changée avec succès !",
            "choose_language": "🌐 Choisissez votre langue :",
            "commands_list": """
📋 **Commandes Disponibles :**

▸ `/start` - Message de bienvenue
▸ `/help` - Aide détaillée
▸ `/info <uid>` - Informations joueur
▸ `/check <uid>` - Vérification statut
▸ `/events [région]` - Événements jeu
▸ `/spam <uid>` - Service spam
▸ `/likes <uid>` - Service likes
▸ `/language` - Changer langue
▸ `/ping` - Tester réponse
▸ `/getgroupid` - ID groupe (Admin)
▸ `/stats` - Statistiques bot (Admin)
            """
        }
        
        # Arabic
        ar_texts = {
            "welcome_message": """
🔥 **مرحباً بك في CH9AYFA Bot v2.0!**

أنا مساعدك المتقدم في Free Fire مع ميزات محسّنة وأداء أفضل.

🚀 **الجديد في v2.0:**
• أوقات استجابة أسرع
• أمان محسّن
• معالجة أخطاء أفضل
• تحليلات فورية
• تخزين مؤقت محسّن

📋 **الأوامر المتاحة:**
• `/help` - دليل المساعدة التفصيلي
• `/info <uid>` - معلومات اللاعب
• `/check <uid>` - فحص حالة اللاعب
• `/events [المنطقة]` - عرض الأحداث
• `/spam <uid>` - خدمة الرسائل
• `/likes <uid>` - خدمة الإعجابات
• `/language` - تغيير اللغة
• `/ping` - اختبار استجابة البوت

💡 **نصيحة:** استخدم `/help` للحصول على شرح مفصل.

🔒 **الأمان:** هذا البوت مقيد للمستخدمين المصرح لهم فقط.
            """,
            "help_message": """
📚 **CH9AYFA Bot v2.0 - دليل المساعدة**

🔍 **أوامر اللاعب:**
• `/info <uid>` - معلومات مفصلة عن اللاعب
  مثال: `/info 2511293320`

• `/check <uid>` - فحص حالة اللاعب
  مثال: `/check 123456789`

📅 **الأحداث:**
• `/events` - أحداث منطقة الشرق الأوسط
• `/events <المنطقة>` - أحداث منطقة محددة
  مثال: `/events NA`

📧 **الخدمات:**
• `/spam <uid>` - إرسال رسائل للاعب
• `/likes <uid>` - إرسال إعجابات للاعب

🌐 **الإعدادات:**
• `/language` - تغيير لغة البوت
• `/ping` - اختبار وقت الاستجابة

👨‍💼 **أوامر المشرف:**
• `/getgroupid` - الحصول على معرف المجموعة
• `/stats` - عرض إحصائيات البوت

❓ **تحتاج مساعدة؟**
اتصل بفريق التطوير للدعم.

🔥 **CH9AYFA Bot v2.0** - مساعد Free Fire المحسّن
            """,
            "unknown_command": "❌ أمر غير معروف. استخدم `/help` لرؤية الأوامر المتاحة.",
            "unauthorized_access": "❌ تم رفض الوصول. هذا البوت مقيد للمستخدمين المصرح لهم فقط.",
            "error_occurred": "❌ حدث خطأ. يرجى المحاولة مرة أخرى لاحقاً.",
            "loading": "⏳ جاري التحميل...",
            "success": "✅ نجح!",
            "failed": "❌ فشل!",
            "player_not_found": "❌ لم يتم العثور على اللاعب.",
            "invalid_uid": "❌ تنسيق UID غير صحيح.",
            "rate_limit_exceeded": "⚠️ تم تجاوز حد المعدل. يرجى الانتظار قبل إرسال أمر آخر.",
            "command_disabled": "❌ هذا الأمر معطل حالياً.",
            "admin_only": "❌ هذا الأمر مخصص للمشرفين فقط.",
            "language_changed": "✅ تم تغيير اللغة بنجاح!",
            "choose_language": "🌐 اختر لغتك:",
            "commands_list": """
📋 **الأوامر المتاحة:**

▸ `/start` - رسالة الترحيب
▸ `/help` - مساعدة مفصلة
▸ `/info <uid>` - معلومات اللاعب
▸ `/check <uid>` - فحص الحالة
▸ `/events [المنطقة]` - أحداث اللعبة
▸ `/spam <uid>` - خدمة الرسائل
▸ `/likes <uid>` - خدمة الإعجابات
▸ `/language` - تغيير اللغة
▸ `/ping` - اختبار الاستجابة
▸ `/getgroupid` - معرف المجموعة (مشرف)
▸ `/stats` - إحصائيات البوت (مشرف)
            """
        }
        
        # Save language files
        language_data = {
            'en': en_texts,
            'fr': fr_texts,
            'ar': ar_texts
        }
        
        for lang_code, texts in language_data.items():
            lang_file = self.languages_dir / f"{lang_code}.json"
            
            if not lang_file.exists():
                try:
                    with open(lang_file, 'w', encoding='utf-8') as f:
                        json.dump(texts, f, indent=2, ensure_ascii=False)
                    logger.info(f"Created default language file: {lang_file}")
                except Exception as e:
                    logger.error(f"Failed to create language file {lang_file}: {e}")
    
    def _get_default_texts(self) -> Dict[str, str]:
        """Get default English texts as fallback"""
        return {
            "welcome_message": "🔥 Welcome to CH9AYFA Bot v2.0!",
            "help_message": "📚 CH9AYFA Bot v2.0 - Help Guide",
            "unknown_command": "❌ Unknown command. Use /help to see available commands.",
            "unauthorized_access": "❌ Access denied.",
            "error_occurred": "❌ An error occurred.",
            "loading": "⏳ Loading...",
            "success": "✅ Success!",
            "failed": "❌ Failed!",
            "player_not_found": "❌ Player not found.",
            "invalid_uid": "❌ Invalid UID format.",
            "rate_limit_exceeded": "⚠️ Rate limit exceeded.",
            "command_disabled": "❌ This command is currently disabled.",
            "admin_only": "❌ This command is reserved for administrators.",
            "language_changed": "✅ Language changed successfully!",
            "choose_language": "🌐 Choose your language:",
            "commands_list": "📋 Available Commands: /start, /help, /info, /check, /events, /spam, /likes, /language"
        }
    
    def get_text(self, language: str, key: str, **kwargs) -> str:
        """Get localized text with fallback support"""
        # Validate language
        if language not in self.supported_languages:
            language = self.default_language
        
        # Get text from language data
        if language in self.languages:
            text = self.languages[language].get(key)
            if text:
                # Format text with provided kwargs
                try:
                    return text.format(**kwargs) if kwargs else text
                except KeyError as e:
                    logger.warning(f"Missing format key {e} for text '{key}' in language '{language}'")
                    return text
        
        # Fallback to default language
        if language != self.default_language and self.default_language in self.languages:
            text = self.languages[self.default_language].get(key)
            if text:
                try:
                    return text.format(**kwargs) if kwargs else text
                except KeyError:
                    return text
        
        # Final fallback
        logger.warning(f"Text key '{key}' not found in any language")
        return f"[{key}]"
    
    def get_help_text(self, language: str) -> str:
        """Get help text for specified language"""
        return self.get_text(language, 'help_message')
    
    def get_welcome_text(self, language: str) -> str:
        """Get welcome text for specified language"""
        return self.get_text(language, 'welcome_message')
    
    def get_commands_list(self, language: str) -> str:
        """Get commands list for specified language"""
        return self.get_text(language, 'commands_list')
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages"""
        return self.supported_languages.copy()
    
    def is_language_supported(self, language: str) -> bool:
        """Check if language is supported"""
        return language in self.supported_languages
    
    def add_language(self, language_code: str, texts: Dict[str, str]) -> bool:
        """Add a new language"""
        try:
            # Validate required keys
            required_keys = ['welcome_message', 'help_message', 'unknown_command']
            missing_keys = [key for key in required_keys if key not in texts]
            
            if missing_keys:
                logger.error(f"Missing required keys for language {language_code}: {missing_keys}")
                return False
            
            # Save to memory
            self.languages[language_code] = texts
            
            # Save to file
            lang_file = self.languages_dir / f"{language_code}.json"
            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump(texts, f, indent=2, ensure_ascii=False)
            
            # Add to supported languages if not already there
            if language_code not in self.supported_languages:
                self.supported_languages.append(language_code)
            
            logger.info(f"Added new language: {language_code}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add language {language_code}: {e}")
            return False
    
    def reload_languages(self):
        """Reload all language files"""
        self.languages.clear()
        self._load_languages()
        logger.info("Languages reloaded")
    
    def get_language_stats(self) -> Dict[str, Any]:
        """Get language statistics"""
        stats = {
            'supported_languages': len(self.supported_languages),
            'loaded_languages': len(self.languages),
            'default_language': self.default_language,
            'languages': {}
        }
        
        for lang_code, texts in self.languages.items():
            stats['languages'][lang_code] = {
                'text_count': len(texts),
                'file_exists': (self.languages_dir / f"{lang_code}.json").exists()
            }
        
        return stats

# Global language manager instance
language_manager_instance = None

def get_language_manager(languages_dir: str = None) -> LanguageManager:
    """Get global language manager instance"""
    global language_manager_instance
    if language_manager_instance is None:
        language_manager_instance = LanguageManager(languages_dir)
    return language_manager_instance

if __name__ == "__main__":
    # Test language manager
    lm = LanguageManager()
    
    print("Testing Language Manager...")
    
    # Test getting texts in different languages
    for lang in ['en', 'fr', 'ar']:
        print(f"\n--- {lang.upper()} ---")
        print("Welcome:", lm.get_text(lang, 'welcome_message')[:100] + "...")
        print("Unknown command:", lm.get_text(lang, 'unknown_command'))
    
    # Test stats
    stats = lm.get_language_stats()
    print(f"\nLanguage Stats: {stats}")
    
    print("\n✅ Language Manager test completed!")
