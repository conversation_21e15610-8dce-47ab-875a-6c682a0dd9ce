#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple launcher for CH9AYFA Bot System
Choose to run Bot only, Admin Panel only, or both
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def run_bot_only():
    """Run only the Telegram bot"""
    print("🤖 Starting Telegram Bot only...")
    import telegram_bot

def run_admin_only():
    """Run only the Flask admin panel"""
    print("🌐 Starting Admin Panel only...")
    from admin_panel import app
    
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"🌐 Admin Panel will be available at: http://localhost:{port}")
    print("👤 Default login: admin / admin123")
    print("🔧 Use Ctrl+C to stop")
    
    app.run(host='0.0.0.0', port=port, debug=debug)

def run_both():
    """Run both services using main.py"""
    print("🚀 Starting both Telegram Bot and Admin Panel...")
    import main
    main.main()

def main():
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                    🔥 SOUHAIL BOT LAUNCHER 🔥                 ║
╚═══════════════════════════════════════════════════════════════╝

Choisissez une option:

1. 🤖 Lancer uniquement le Bot Telegram
2. 🌐 Lancer uniquement le Panel d'Administration
3. 🚀 Lancer les deux services
4. ❌ Quitter

""")
    
    while True:
        try:
            choice = input("Votre choix (1-4): ").strip()
            
            if choice == '1':
                run_bot_only()
                break
            elif choice == '2':
                run_admin_only()
                break
            elif choice == '3':
                run_both()
                break
            elif choice == '4':
                print("👋 Au revoir!")
                sys.exit(0)
            else:
                print("❌ Choix invalide. Veuillez choisir entre 1 et 4.")
                
        except KeyboardInterrupt:
            print("\n👋 Au revoir!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
