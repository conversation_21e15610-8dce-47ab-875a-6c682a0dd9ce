#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CH9AYFA Bot System v2.0 - Advanced Telegram Bot
Enhanced bot with modular architecture, async support, and advanced features
"""

import os
import sys
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
import logging
from functools import wraps

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import telebot
from telebot import types
from telebot.async_telebot import AsyncTeleBot

# Import v2 modules
from config.settings import get_settings
from src.database_manager import get_database_manager, UserData, CommandLog

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramBotV2:
    """Advanced Telegram Bot with enhanced features"""
    
    def __init__(self):
        self.settings = get_settings()
        self.db = get_database_manager(
            self.settings.database.firebase_credentials_path,
            self.settings.database.local_db_path,
            self.settings.database.use_local_fallback
        )
        self.api_client = APIClient(self.settings.api)
        self.language_manager = LanguageManager()
        self.security_manager = SecurityManager(self.settings.security)
        
        # Initialize bot
        self.bot = AsyncTeleBot(self.settings.telegram.bot_token)
        
        # Command handlers registry
        self.command_handlers: Dict[str, Callable] = {}
        self.middleware_stack: List[Callable] = []
        
        # Performance metrics
        self.metrics = {
            'commands_processed': 0,
            'errors_count': 0,
            'average_response_time': 0.0,
            'uptime_start': datetime.now()
        }
        
        # Register middleware and handlers
        self._register_middleware()
        self._register_handlers()
        
        logger.info("✅ CH9AYFA Bot v2.0 initialized successfully")
    
    def _register_middleware(self):
        """Register middleware functions"""
        self.middleware_stack = [
            self._security_middleware,
            self._analytics_middleware,
            self._rate_limit_middleware,
            self._user_update_middleware
        ]
    
    def _register_handlers(self):
        """Register command handlers"""
        handlers = {
            'start': self._handle_start,
            'help': self._handle_help,
            'info': self._handle_info,
            'check': self._handle_check,
            'events': self._handle_events,
            'spam': self._handle_spam,
            'likes': self._handle_likes,
            'language': self._handle_language,
            'getgroupid': self._handle_get_group_id,
            'stats': self._handle_stats,
            'ping': self._handle_ping
        }
        
        for command, handler in handlers.items():
            self.command_handlers[command] = handler
            self.bot.register_message_handler(
                self._create_handler_wrapper(handler),
                commands=[command]
            )
        
        # Register callback handlers
        self.bot.register_callback_query_handler(
            self._handle_language_callback,
            func=lambda call: call.data.startswith('lang_')
        )
        
        # Register unknown command handler
        self.bot.register_message_handler(
            self._handle_unknown,
            func=lambda message: True
        )
    
    def _create_handler_wrapper(self, handler: Callable):
        """Create a wrapper for command handlers with middleware support"""
        @wraps(handler)
        async def wrapper(message):
            start_time = time.time()
            
            try:
                # Run middleware stack
                for middleware in self.middleware_stack:
                    result = await middleware(message)
                    if result is False:  # Middleware blocked the request
                        return
                
                # Execute the handler
                await handler(message)
                
                # Update metrics
                execution_time = time.time() - start_time
                self._update_metrics(True, execution_time)
                
            except Exception as e:
                logger.error(f"Error in handler {handler.__name__}: {e}")
                self._update_metrics(False, time.time() - start_time)
                
                # Send error message to user
                await self._send_error_message(message, str(e))
        
        return wrapper
    
    async def _security_middleware(self, message) -> bool:
        """Security middleware - check authorization"""
        if not self._is_authorized(message):
            await self._send_unauthorized_message(message)
            return False
        return True
    
    async def _analytics_middleware(self, message) -> bool:
        """Analytics middleware - log command usage"""
        if self.settings.features.enable_analytics:
            command = message.text.split()[0].replace('/', '') if message.text else 'unknown'
            
            command_log = CommandLog(
                user_id=message.from_user.id,
                command=f"/{command}",
                success=True,  # Will be updated later if there's an error
                timestamp=datetime.now(),
                metadata={
                    'chat_id': message.chat.id,
                    'chat_type': message.chat.type,
                    'username': message.from_user.username
                }
            )
            
            await self.db.log_command(command_log)
        
        return True
    
    async def _rate_limit_middleware(self, message) -> bool:
        """Rate limiting middleware"""
        if self.settings.security.rate_limit_enabled:
            user_id = message.from_user.id
            if not self.security_manager.check_rate_limit(user_id):
                await self.bot.reply_to(
                    message,
                    "⚠️ Rate limit exceeded. Please wait before sending another command."
                )
                return False
        return True
    
    async def _user_update_middleware(self, message) -> bool:
        """User update middleware - update user information"""
        try:
            user = await self.db.get_user(message.from_user.id)
            
            if user is None:
                # Create new user
                user = UserData(
                    user_id=message.from_user.id,
                    username=message.from_user.username,
                    first_name=message.from_user.first_name,
                    last_name=message.from_user.last_name,
                    language='en'
                )
            else:
                # Update existing user
                user.username = message.from_user.username
                user.first_name = message.from_user.first_name
                user.last_name = message.from_user.last_name
                user.last_seen = datetime.now()
                user.command_count += 1
            
            await self.db.save_user(user)
            
        except Exception as e:
            logger.error(f"Error updating user data: {e}")
        
        return True
    
    def _is_authorized(self, message) -> bool:
        """Check if user is authorized to use the bot"""
        user_id = message.from_user.id
        chat_id = message.chat.id
        
        # Check if user is admin
        if user_id in self.settings.telegram.admin_user_ids:
            return True
        
        # Check if message is from developer group
        if self.settings.telegram.developer_group_id:
            if str(chat_id) == str(self.settings.telegram.developer_group_id):
                return True
        
        # Check if it's a private chat with authorized user
        if message.chat.type == 'private' and user_id in self.settings.telegram.admin_user_ids:
            return True
        
        return False
    
    async def _send_unauthorized_message(self, message):
        """Send unauthorized access message"""
        text = "❌ Access denied. This bot is restricted to authorized users only."
        await self.bot.reply_to(message, text)
    
    async def _send_error_message(self, message, error: str):
        """Send error message to user"""
        text = f"❌ An error occurred: {error}\n\nPlease try again later or contact support."
        await self.bot.reply_to(message, text)
    
    def _update_metrics(self, success: bool, execution_time: float):
        """Update performance metrics"""
        self.metrics['commands_processed'] += 1
        
        if not success:
            self.metrics['errors_count'] += 1
        
        # Update average response time
        current_avg = self.metrics['average_response_time']
        total_commands = self.metrics['commands_processed']
        
        self.metrics['average_response_time'] = (
            (current_avg * (total_commands - 1) + execution_time) / total_commands
        )
    
    async def _handle_start(self, message):
        """Handle /start command"""
        user_id = message.from_user.id
        user = await self.db.get_user(user_id)
        language = user.language if user else 'en'
        
        welcome_text = self.language_manager.get_text(language, 'welcome_message')
        
        # Create inline keyboard for quick actions
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("📋 Help", callback_data="help"),
            types.InlineKeyboardButton("🌐 Language", callback_data="language")
        )
        
        await self.bot.reply_to(message, welcome_text, reply_markup=markup)
    
    async def _handle_help(self, message):
        """Handle /help command"""
        user_id = message.from_user.id
        user = await self.db.get_user(user_id)
        language = user.language if user else 'en'
        
        help_text = self.language_manager.get_help_text(language)
        await self.bot.reply_to(message, help_text, parse_mode='Markdown')
    
    async def _handle_info(self, message):
        """Handle /info command"""
        if not self.settings.features.enable_info_command:
            await self.bot.reply_to(message, "❌ This command is currently disabled.")
            return
        
        # Extract UID from command
        parts = message.text.split()
        if len(parts) < 2:
            await self.bot.reply_to(
                message,
                "❌ Please provide a UID.\nUsage: `/info <uid>`",
                parse_mode='Markdown'
            )
            return
        
        uid = parts[1]
        
        try:
            # Show loading message
            loading_msg = await self.bot.reply_to(message, "🔍 Fetching player information...")
            
            # Get player info from API
            player_info = await self.api_client.get_player_info(uid)
            
            if player_info:
                # Format and send player information
                info_text = self._format_player_info(player_info)
                await self.bot.edit_message_text(
                    info_text,
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
            else:
                await self.bot.edit_message_text(
                    "❌ Player not found or API error.",
                    message.chat.id,
                    loading_msg.message_id
                )
                
        except Exception as e:
            logger.error(f"Error in info command: {e}")
            await self.bot.reply_to(message, f"❌ Error: {str(e)}")
    
    async def _handle_check(self, message):
        """Handle /check command"""
        # Extract UID from command
        parts = message.text.split()
        if len(parts) < 2:
            await self.bot.reply_to(
                message,
                "❌ Please provide a UID.\nUsage: `/check <uid>`",
                parse_mode='Markdown'
            )
            return
        
        uid = parts[1]
        
        try:
            # Show loading message
            loading_msg = await self.bot.reply_to(message, "⏳ Checking player status...")
            
            # Check player status
            status = await self.api_client.check_player_status(uid)
            
            if status:
                status_text = f"✅ **Player Status Check**\n\n"
                status_text += f"🆔 **UID:** `{uid}`\n"
                status_text += f"📊 **Status:** {status.get('status', 'Unknown')}\n"
                status_text += f"🕐 **Last Check:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                
                await self.bot.edit_message_text(
                    status_text,
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
            else:
                await self.bot.edit_message_text(
                    "❌ Unable to check player status.",
                    message.chat.id,
                    loading_msg.message_id
                )
                
        except Exception as e:
            logger.error(f"Error in check command: {e}")
            await self.bot.reply_to(message, f"❌ Error: {str(e)}")
    
    async def _handle_events(self, message):
        """Handle /events command"""
        if not self.settings.features.enable_events_command:
            await self.bot.reply_to(message, "❌ This command is currently disabled.")
            return
        
        # Extract region from command (optional)
        parts = message.text.split()
        region = parts[1] if len(parts) > 1 else 'ME'
        
        try:
            # Show loading message
            loading_msg = await self.bot.reply_to(message, "📅 Fetching game events...")
            
            # Get events from API
            events = await self.api_client.get_events(region)
            
            if events:
                events_text = self._format_events(events, region)
                await self.bot.edit_message_text(
                    events_text,
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
            else:
                await self.bot.edit_message_text(
                    f"❌ No events found for region {region}.",
                    message.chat.id,
                    loading_msg.message_id
                )
                
        except Exception as e:
            logger.error(f"Error in events command: {e}")
            await self.bot.reply_to(message, f"❌ Error: {str(e)}")
    
    async def _handle_spam(self, message):
        """Handle /spam command"""
        if not self.settings.features.enable_spam_command:
            await self.bot.reply_to(message, "❌ This command is currently disabled.")
            return
        
        # Extract UID from command
        parts = message.text.split()
        if len(parts) < 2:
            await self.bot.reply_to(
                message,
                "❌ Please provide a UID.\nUsage: `/spam <uid>`",
                parse_mode='Markdown'
            )
            return
        
        uid = parts[1]
        
        try:
            # Show loading message
            loading_msg = await self.bot.reply_to(message, "📧 Sending spam...")
            
            # Send spam via API
            result = await self.api_client.send_spam(uid)
            
            if result and result.get('success'):
                await self.bot.edit_message_text(
                    f"✅ Spam sent successfully to UID: `{uid}`",
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
            else:
                await self.bot.edit_message_text(
                    f"❌ Failed to send spam to UID: `{uid}`",
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
                
        except Exception as e:
            logger.error(f"Error in spam command: {e}")
            await self.bot.reply_to(message, f"❌ Error: {str(e)}")
    
    async def _handle_likes(self, message):
        """Handle /likes command"""
        if not self.settings.features.enable_likes_command:
            await self.bot.reply_to(message, "❌ This command is currently disabled.")
            return
        
        # Extract UID from command
        parts = message.text.split()
        if len(parts) < 2:
            await self.bot.reply_to(
                message,
                "❌ Please provide a UID.\nUsage: `/likes <uid>`",
                parse_mode='Markdown'
            )
            return
        
        uid = parts[1]
        
        try:
            # Show loading message
            loading_msg = await self.bot.reply_to(message, "❤️ Sending likes...")
            
            # Send likes via API
            result = await self.api_client.send_likes(uid)
            
            if result and result.get('success'):
                await self.bot.edit_message_text(
                    f"✅ Likes sent successfully to UID: `{uid}`",
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
            else:
                await self.bot.edit_message_text(
                    f"❌ Failed to send likes to UID: `{uid}`",
                    message.chat.id,
                    loading_msg.message_id,
                    parse_mode='Markdown'
                )
                
        except Exception as e:
            logger.error(f"Error in likes command: {e}")
            await self.bot.reply_to(message, f"❌ Error: {str(e)}")
    
    async def _handle_language(self, message):
        """Handle /language command"""
        if not self.settings.features.enable_language_command:
            await self.bot.reply_to(message, "❌ This command is currently disabled.")
            return
        
        # Create language selection keyboard
        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("🇫🇷 Français", callback_data="lang_fr"),
            types.InlineKeyboardButton("🇬🇧 English", callback_data="lang_en"),
            types.InlineKeyboardButton("🇸🇦 العربية", callback_data="lang_ar")
        )
        
        await self.bot.reply_to(
            message,
            "🌐 **Choose your language:**",
            reply_markup=markup,
            parse_mode='Markdown'
        )
    
    async def _handle_language_callback(self, call):
        """Handle language selection callback"""
        try:
            user_id = call.from_user.id
            language_code = call.data.split('_')[1]
            
            # Update user language
            user = await self.db.get_user(user_id)
            if user:
                user.language = language_code
                await self.db.save_user(user)
            
            # Send confirmation
            language_name = {
                'fr': '🇫🇷 Français',
                'en': '🇬🇧 English',
                'ar': '🇸🇦 العربية'
            }.get(language_code, language_code)
            
            await self.bot.edit_message_text(
                f"✅ Language changed to {language_name}!",
                call.message.chat.id,
                call.message.message_id
            )
            
            await self.bot.answer_callback_query(call.id, f"Language set to {language_name}")
            
        except Exception as e:
            logger.error(f"Error in language callback: {e}")
            await self.bot.answer_callback_query(call.id, "❌ Error changing language")
    
    async def _handle_get_group_id(self, message):
        """Handle /getgroupid command (admin only)"""
        if not self.settings.features.enable_admin_commands:
            await self.bot.reply_to(message, "❌ Admin commands are currently disabled.")
            return
        
        user_id = message.from_user.id
        
        # Check if user is admin
        if user_id not in self.settings.telegram.admin_user_ids:
            await self.bot.reply_to(message, "❌ This command is reserved for administrators.")
            return
        
        chat_id = message.chat.id
        chat_type = message.chat.type
        chat_title = getattr(message.chat, 'title', 'N/A')
        
        info_text = f"""
╔═══════════════════════════════╗
║        📋 CHAT INFO           ║
╚═══════════════════════════════╝

🆔 **Chat ID:** `{chat_id}`
📝 **Chat Type:** `{chat_type}`
🏷️ **Chat Title:** `{chat_title}`

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💡 **To configure this group as developer group:**
Add this line to your .env file:
`DEVELOPER_GROUP_ID={chat_id}`
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        
        await self.bot.reply_to(message, info_text, parse_mode='Markdown')
    
    async def _handle_stats(self, message):
        """Handle /stats command (admin only)"""
        user_id = message.from_user.id
        
        # Check if user is admin
        if user_id not in self.settings.telegram.admin_user_ids:
            await self.bot.reply_to(message, "❌ This command is reserved for administrators.")
            return
        
        try:
            # Get analytics from database
            analytics = await self.db.get_analytics()
            
            # Get bot metrics
            uptime = datetime.now() - self.metrics['uptime_start']
            uptime_str = str(uptime).split('.')[0]  # Remove microseconds
            
            stats_text = f"""
📊 **Bot Statistics**

👥 **Users:**
• Total: {analytics.get('total_users', 0)}
• Active (7 days): {analytics.get('active_users', 0)}

⚡ **Commands:**
• Total processed: {analytics.get('total_commands', 0)}
• Success rate: {analytics.get('success_rate', 0)}%
• Bot processed: {self.metrics['commands_processed']}
• Errors: {self.metrics['errors_count']}

🚀 **Performance:**
• Uptime: {uptime_str}
• Avg response time: {self.metrics['average_response_time']:.2f}s

🕐 **Last updated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            await self.bot.reply_to(message, stats_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in stats command: {e}")
            await self.bot.reply_to(message, f"❌ Error getting statistics: {str(e)}")
    
    async def _handle_ping(self, message):
        """Handle /ping command"""
        start_time = time.time()
        
        ping_msg = await self.bot.reply_to(message, "🏓 Pong!")
        
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        await self.bot.edit_message_text(
            f"🏓 Pong! Response time: {response_time:.2f}ms",
            message.chat.id,
            ping_msg.message_id
        )
    
    async def _handle_unknown(self, message):
        """Handle unknown commands"""
        user_id = message.from_user.id
        user = await self.db.get_user(user_id)
        language = user.language if user else 'en'
        
        unknown_text = self.language_manager.get_text(language, 'unknown_command')
        help_text = self.language_manager.get_commands_list(language)
        
        full_text = f"{unknown_text}\n\n{help_text}"
        
        await self.bot.reply_to(message, full_text, parse_mode='Markdown')
    
    def _format_player_info(self, player_info: Dict[str, Any]) -> str:
        """Format player information for display"""
        # This would format the player info based on the API response structure
        # Implementation depends on the actual API response format
        return f"👤 **Player Information**\n\nUID: `{player_info.get('uid', 'N/A')}`\nName: {player_info.get('name', 'N/A')}"
    
    def _format_events(self, events: List[Dict[str, Any]], region: str) -> str:
        """Format events for display"""
        # This would format the events based on the API response structure
        # Implementation depends on the actual API response format
        events_text = f"📅 **Game Events - {region}**\n\n"
        
        for i, event in enumerate(events[:5], 1):  # Show max 5 events
            events_text += f"{i}. {event.get('name', 'Unknown Event')}\n"
        
        return events_text
    
    async def start_polling(self):
        """Start the bot with polling"""
        logger.info("🚀 Starting CH9AYFA Bot v2.0 with polling...")
        await self.bot.polling(non_stop=True)
    
    async def start_webhook(self, webhook_url: str, webhook_secret: str = None):
        """Start the bot with webhook"""
        logger.info(f"🚀 Starting CH9AYFA Bot v2.0 with webhook: {webhook_url}")
        
        # Set webhook
        await self.bot.set_webhook(
            url=webhook_url,
            secret_token=webhook_secret
        )
        
        logger.info("✅ Webhook set successfully")
    
    def stop(self):
        """Stop the bot"""
        logger.info("🛑 Stopping CH9AYFA Bot v2.0...")
        # Cleanup code here if needed

# Global bot instance
bot_instance = None

def get_bot() -> TelegramBotV2:
    """Get global bot instance"""
    global bot_instance
    if bot_instance is None:
        bot_instance = TelegramBotV2()
    return bot_instance

async def main():
    """Main function to run the bot"""
    try:
        bot = get_bot()
        await bot.start_polling()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
