#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for CH9AYFA Bot System
Tests both Telegram Bot and Flask Admin Panel functionality
"""

import os
import sys
import time
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment_variables():
    """Test if all required environment variables are set"""
    print("🔍 Testing Environment Variables...")
    
    required_vars = {
        'TELEGRAM_BOT_TOKEN': 'Token du bot Telegram',
        'API_KEY': 'Clé API',
        'DEVELOPER_GROUP_ID': 'ID du groupe développeur',
        'ADMIN_USER_IDS': 'IDs des administrateurs',
        'FLASK_SECRET_KEY': 'Clé secrète Flask',
        'FLASK_PORT': 'Port Flask',
        'FLASK_DEBUG': 'Mode debug Flask'
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            print(f"  ✅ {var}: {description} - {'*' * min(len(value), 10)}...")
        else:
            print(f"  ❌ {var}: {description} - MANQUANT")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ Variables manquantes: {', '.join(missing_vars)}")
        print("Veuillez configurer votre fichier .env")
        return False
    
    print("✅ Toutes les variables d'environnement sont configurées")
    return True

def test_firebase_connection():
    """Test Firebase connection"""
    print("\n🔥 Testing Firebase Connection...")
    
    try:
        from firebase_db import get_firebase_manager
        
        firebase_db = get_firebase_manager()
        
        if firebase_db.firebase_enabled:
            print("  ✅ Firebase connecté et fonctionnel")
            
            # Test basic operations
            test_user_id = 999999999
            firebase_db.set_user_language(test_user_id, 'fr')
            lang = firebase_db.get_user_language(test_user_id)
            
            if lang == 'fr':
                print("  ✅ Test de lecture/écriture Firebase réussi")
            else:
                print("  ⚠️ Test de lecture/écriture Firebase échoué")
            
            return True
        else:
            print("  ⚠️ Firebase non disponible, utilisation du mode local")
            return True
            
    except Exception as e:
        print(f"  ❌ Erreur Firebase: {e}")
        return False

def test_telegram_bot_imports():
    """Test if Telegram bot can be imported"""
    print("\n🤖 Testing Telegram Bot Imports...")
    
    try:
        import telegram_bot
        print("  ✅ Module telegram_bot importé avec succès")
        
        # Test bot configuration
        if hasattr(telegram_bot, 'BOT_TOKEN') and telegram_bot.BOT_TOKEN:
            print("  ✅ Token du bot configuré")
        else:
            print("  ❌ Token du bot manquant")
            return False
        
        if hasattr(telegram_bot, 'DEVELOPER_GROUP_ID'):
            print(f"  ✅ Groupe développeur configuré: {telegram_bot.DEVELOPER_GROUP_ID}")
        else:
            print("  ⚠️ Groupe développeur non configuré")
        
        if hasattr(telegram_bot, 'ADMIN_USER_IDS') and telegram_bot.ADMIN_USER_IDS:
            print(f"  ✅ Administrateurs configurés: {len(telegram_bot.ADMIN_USER_IDS)} admin(s)")
        else:
            print("  ⚠️ Aucun administrateur configuré")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur d'import du bot Telegram: {e}")
        return False

def test_flask_app_imports():
    """Test if Flask app can be imported"""
    print("\n🌐 Testing Flask App Imports...")
    
    try:
        from admin_panel import app
        print("  ✅ Application Flask importée avec succès")
        
        # Test Flask configuration
        if app.secret_key:
            print("  ✅ Clé secrète Flask configurée")
        else:
            print("  ❌ Clé secrète Flask manquante")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur d'import de l'application Flask: {e}")
        return False

def test_flask_app_routes():
    """Test Flask app routes (if running)"""
    print("\n🌐 Testing Flask App Routes...")
    
    port = os.getenv('FLASK_PORT', '5000')
    base_url = f"http://localhost:{port}"
    
    try:
        # Test if Flask app is running
        response = requests.get(f"{base_url}/", timeout=5)
        
        if response.status_code == 200 or response.status_code == 302:  # 302 for redirect to login
            print("  ✅ Application Flask accessible")
            
            # Test login page
            login_response = requests.get(f"{base_url}/login", timeout=5)
            if login_response.status_code == 200:
                print("  ✅ Page de connexion accessible")
            else:
                print("  ⚠️ Page de connexion non accessible")
            
            return True
        else:
            print(f"  ⚠️ Application Flask non accessible (Status: {response.status_code})")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ⚠️ Application Flask non démarrée")
        print(f"    Pour tester, lancez: python admin_panel.py")
        return False
    except Exception as e:
        print(f"  ❌ Erreur lors du test Flask: {e}")
        return False

def test_api_endpoints():
    """Test external API endpoints"""
    print("\n🌍 Testing External API Endpoints...")
    
    api_endpoints = {
        'Info API': 'https://info-ch9ayfa.vercel.app/2511293320',
        'Check API': 'https://ch9ayfa-check-1.vercel.app/check_status?key=ch9ayfa&uid=2511293320',
        'Events API': 'https://eventes-ch9ayfa.vercel.app/eventes?region=ME&key=ch9ayfa',
        'Spam API': 'https://spam-ch9ayfa.vercel.app/spam?id=2511293320',
        'Likes API': 'https://likes-ch9ayfa-v6.vercel.app/like?uid=2511293320&key=ch9ayfa-6'
    }
    
    working_apis = 0
    total_apis = len(api_endpoints)
    
    for name, url in api_endpoints.items():
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"  ✅ {name}: Accessible")
                working_apis += 1
            else:
                print(f"  ⚠️ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name}: Erreur - {str(e)[:50]}...")
    
    print(f"\n📊 APIs fonctionnelles: {working_apis}/{total_apis}")
    return working_apis > 0

def run_all_tests():
    """Run all tests"""
    print("🧪 CH9AYFA Bot System - Tests de Fonctionnement")
    print("=" * 60)
    
    tests = [
        ("Variables d'environnement", test_environment_variables),
        ("Connexion Firebase", test_firebase_connection),
        ("Bot Telegram", test_telegram_bot_imports),
        ("Application Flask", test_flask_app_imports),
        ("Routes Flask", test_flask_app_routes),
        ("APIs externes", test_api_endpoints)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"  ❌ Erreur inattendue dans {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 RÉSULTATS: {passed_tests}/{total_tests} tests réussis")
    
    if passed_tests == total_tests:
        print("🎉 Tous les tests sont passés! Le système est prêt.")
    elif passed_tests >= total_tests - 2:
        print("✅ La plupart des tests sont passés. Le système devrait fonctionner.")
    else:
        print("⚠️ Plusieurs tests ont échoué. Vérifiez la configuration.")
    
    print("\n🚀 Pour démarrer le système:")
    print("   python start.py")
    print("\n🌐 Panel d'administration:")
    print(f"   http://localhost:{os.getenv('FLASK_PORT', '5000')}")
    print("   Identifiants: admin / admin123")

if __name__ == "__main__":
    run_all_tests()
