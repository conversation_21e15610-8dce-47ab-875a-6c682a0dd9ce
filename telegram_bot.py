import os
import requests
import telebot
from dotenv import load_dotenv
import json
from typing import Optional
from telebot import types
from firebase_db import get_firebase_manager, init_firebase_db
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot configuration
BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
API_KEY = os.getenv('API_KEY', 'ch9ayfa')

# Developer group configuration
DEVELOPER_GROUP_ID = os.getenv('DEVELOPER_GROUP_ID')  # ID du groupe des développeurs
ADMIN_USER_IDS = [
    int(id.strip()) for id in os.getenv('ADMIN_USER_IDS', '').split(',') if id.strip()
]  # IDs des administrateurs

if not BOT_TOKEN:
    raise ValueError("TELEGRAM_BOT_TOKEN not found in environment variables")

bot = telebot.TeleBot(BOT_TOKEN)

# Initialize Firebase
logger.info("🔥 Initializing Firebase database...")
firebase_db = get_firebase_manager()

# Language translations
LANGUAGES = {
    'fr': {
        'welcome_title': '🔥 CH9AYFA API BOT 🔥',
        'welcome_msg': '🎮 **Bienvenue dans le bot ultime Free Fire !**',
        'commands_title': '📋 COMMANDES',
        'quick_start': '💡 **Démarrage rapide :** Essayez `/info 2511293320`',
        'help_center': '📚 CENTRE D\'AIDE',
        'detailed_guide': '🎯 **GUIDE DÉTAILLÉ DES COMMANDES**',
        'player_info': '🔍 INFORMATIONS JOUEUR',
        'status_check': '✅ VÉRIFICATION STATUT',
        'game_events': '📅 ÉVÉNEMENTS JEU',
        'spam_service': '📧 SERVICE SPAM',
        'likes_service': '❤️ SERVICE LIKES',
        'language_service': '🌐 LANGUE',
        'need_help': '🔥 **Besoin d\'aide ?** Contactez le support !',
        'unknown_command': '🚫 **COMMANDE INCONNUE**',
        'dont_recognize': '❓ Je ne reconnais pas cette commande !',
        'available_commands': '💡 **Commandes disponibles :**',
        'type_help': '🔥 Tapez `/help` pour des instructions détaillées !',
        'select_language': '🌐 **Sélectionnez votre langue**',
        'language_changed': '✅ **Langue changée en Français !**',
        'getting_info': '🔄 Récupération des informations pour UID',
        'provide_uid': '❌ Veuillez fournir un UID. Usage',
        'error': '❌ Erreur',
        'sending_spam': '📧 ENVOI SPAM',
        'sending_likes': '❤️ ENVOI LIKES',
        'target_uid': '🎯 **UID Cible :**',
        'status': '🔄 **Statut :**',
        'processing': 'Traitement...',
        'please_wait': '⏳ **Veuillez patienter...**',
        'success': 'Succès !',
        'failed': 'Échec',
        'try_again': '🔄 **Réessayez plus tard ou vérifiez l\'UID**',
        'mission_accomplished': '🔥 **Mission accomplie !** 🔥',
        'delivered_successfully': 'livré avec succès !',
        'response_details': '📊 **Détails de la réponse :**',
        'events_loading': '⏳ Chargement des événements...',
        'events_found': '🎮 {count} événements trouvés :',
        'events_error': '❌ Erreur lors du chargement des événements',
        'no_events': '📭 Aucun événement trouvé pour cette région',
        'unauthorized_access': '🚫 **Accès Refusé**\n\nCe bot est réservé aux développeurs uniquement.\nContactez un administrateur pour obtenir l\'accès.'
    },
    'en': {
        'welcome_title': '🔥 CH9AYFA API BOT 🔥',
        'welcome_msg': '🎮 **Welcome to the ultimate Free Fire info bot!**',
        'commands_title': '📋 COMMANDS',
        'quick_start': '💡 **Quick Start:** Try `/info 2511293320`',
        'help_center': '📚 HELP CENTER',
        'detailed_guide': '🎯 **DETAILED COMMAND GUIDE**',
        'player_info': '🔍 PLAYER INFO',
        'status_check': '✅ STATUS CHECK',
        'game_events': '📅 GAME EVENTS',
        'spam_service': '📧 SPAM SERVICE',
        'likes_service': '❤️ LIKES SERVICE',
        'language_service': '🌐 LANGUAGE',
        'need_help': '🔥 **Need more help?** Contact support!',
        'unknown_command': '🚫 **UNKNOWN COMMAND**',
        'dont_recognize': '❓ I don\'t recognize that command!',
        'available_commands': '💡 **Available Commands:**',
        'type_help': '🔥 Type `/help` for detailed instructions!',
        'select_language': '🌐 **Select your language**',
        'language_changed': '✅ **Language changed to English!**',
        'getting_info': '🔄 Getting information for UID',
        'provide_uid': '❌ Please provide a UID. Usage',
        'error': '❌ Error',
        'sending_spam': '📧 SENDING SPAM',
        'sending_likes': '❤️ SENDING LIKES',
        'events_loading': '⏳ Loading events...',
        'events_found': '🎮 Found {count} events:',
        'events_error': '❌ Error loading events',
        'no_events': '📭 No events found for this region',
        'unauthorized_access': '🚫 **Access Denied**\n\nThis bot is restricted to developers only.\nContact an administrator for access.',
        'target_uid': '🎯 **Target UID:**',
        'status': '🔄 **Status:**',
        'processing': 'Processing...',
        'please_wait': '⏳ **Please wait...**',
        'success': 'Success!',
        'failed': 'Failed',
        'try_again': '🔄 **Try again later or check the UID**',
        'mission_accomplished': '🔥 **Mission accomplished!** 🔥',
        'delivered_successfully': 'delivered successfully!',
        'response_details': '📊 **Response Details:**'
    },
    'ar': {
        'welcome_title': '🔥 بوت CH9AYFA API 🔥',
        'welcome_msg': '🎮 **مرحباً بك في بوت فري فاير المتقدم!**',
        'commands_title': '📋 الأوامر',
        'quick_start': '💡 **البداية السريعة:** جرب `/info 2511293320`',
        'help_center': '📚 مركز المساعدة',
        'detailed_guide': '🎯 **دليل الأوامر المفصل**',
        'player_info': '🔍 معلومات اللاعب',
        'status_check': '✅ فحص الحالة',
        'game_events': '📅 أحداث اللعبة',
        'spam_service': '📧 خدمة الرسائل',
        'likes_service': '❤️ خدمة الإعجابات',
        'language_service': '🌐 اللغة',
        'need_help': '🔥 **تحتاج مساعدة أكثر؟** اتصل بالدعم!',
        'unknown_command': '🚫 **أمر غير معروف**',
        'dont_recognize': '❓ لا أتعرف على هذا الأمر!',
        'available_commands': '💡 **الأوامر المتاحة:**',
        'type_help': '🔥 اكتب `/help` للحصول على تعليمات مفصلة!',
        'select_language': '🌐 **اختر لغتك**',
        'language_changed': '✅ **تم تغيير اللغة إلى العربية!**',
        'getting_info': '🔄 جاري الحصول على المعلومات لـ UID',
        'provide_uid': '❌ يرجى تقديم UID. الاستخدام',
        'error': '❌ خطأ',
        'sending_spam': '📧 إرسال رسائل',
        'sending_likes': '❤️ إرسال إعجابات',
        'events_loading': '⏳ جاري تحميل الأحداث...',
        'events_found': '🎮 تم العثور على {count} أحداث:',
        'events_error': '❌ خطأ في تحميل الأحداث',
        'no_events': '📭 لم يتم العثور على أحداث لهذه المنطقة',
        'unauthorized_access': '🚫 **تم رفض الوصول**\n\nهذا البوت مقيد للمطورين فقط.\nاتصل بالمدير للحصول على الوصول.',
        'target_uid': '🎯 **UID المستهدف:**',
        'status': '🔄 **الحالة:**',
        'processing': 'جاري المعالجة...',
        'please_wait': '⏳ **يرجى الانتظار...**',
        'success': 'نجح!',
        'failed': 'فشل',
        'try_again': '🔄 **حاول مرة أخرى لاحقاً أو تحقق من UID**',
        'mission_accomplished': '🔥 **تمت المهمة بنجاح!** 🔥',
        'delivered_successfully': 'تم التسليم بنجاح!',
        'response_details': '📊 **تفاصيل الاستجابة:**'
    }
}

def get_user_language(user_id):
    """Get user's preferred language from Firebase, default to English"""
    try:
        return firebase_db.get_user_language(user_id)
    except Exception as e:
        logger.error(f"Error getting user language: {e}")
        return 'en'  # Default to English on error

def get_text(user_id, key):
    """Get translated text for user"""
    lang = get_user_language(user_id)
    return LANGUAGES[lang].get(key, LANGUAGES['en'].get(key, key))

def is_developer_authorized(message):
    """Check if user is authorized (in developer group or admin)"""
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is admin
    if user_id in ADMIN_USER_IDS:
        return True

    # Check if message is from developer group
    if DEVELOPER_GROUP_ID and str(chat_id) == str(DEVELOPER_GROUP_ID):
        return True

    # Check if it's a private chat with authorized user
    if message.chat.type == 'private' and user_id in ADMIN_USER_IDS:
        return True

    return False

def send_unauthorized_message(message):
    """Send unauthorized access message"""
    user_id = message.from_user.id
    unauthorized_msg = get_text(user_id, 'unauthorized_access')
    bot.reply_to(message, unauthorized_msg)

def save_user_info(user_id, user_data):
    """Save user information to Firebase"""
    try:
        return firebase_db.save_user_info(user_id, user_data)
    except Exception as e:
        logger.error(f"Error saving user info: {e}")
        return False

def log_command_usage(user_id, command, success=True):
    """Log command usage to Firebase"""
    try:
        return firebase_db.log_command_usage(user_id, command, success)
    except Exception as e:
        logger.error(f"Error logging command usage: {e}")
        return False

# API endpoints
INFO_API_BASE = "https://info-ch9ayfa.vercel.app"
CHECK_API = "https://ch9ayfa-check-1.vercel.app/check_status"
EVENTS_API = "https://eventes-ch9ayfa.vercel.app/eventes"
SPAM_API = "https://spam-ch9ayfa.vercel.app/spam"
LIKES_API = "https://likes-ch9ayfa-v6.vercel.app/like"

class APIClient:
    @staticmethod
    def get_info(uid: str):
        """Get information from info API with UID"""
        try:
            url = f"{INFO_API_BASE}/{uid}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            return response.json() if response.content else {"status": "success", "data": "Info retrieved", "uid": uid}
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to get info: {str(e)}"}
    
    @staticmethod
    def check_status(uid: str):
        """Check status for a specific UID"""
        try:
            params = {"key": API_KEY, "uid": uid}
            response = requests.get(CHECK_API, params=params, timeout=10)
            response.raise_for_status()
            return response.json() if response.content else {"status": "checked", "uid": uid}
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to check status: {str(e)}"}
    
    @staticmethod
    def get_events(region: str = "ME"):
        """Get events for a specific region"""
        try:
            params = {"region": region, "key": API_KEY}
            response = requests.get(EVENTS_API, params=params, timeout=10)
            response.raise_for_status()
            return response.json() if response.content else {"status": "success", "region": region}
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to get events: {str(e)}"}
    
    @staticmethod
    def send_spam(uid: str):
        """Send spam to specific UID"""
        try:
            params = {"id": uid}
            response = requests.get(SPAM_API, params=params, timeout=10)
            response.raise_for_status()
            return response.json() if response.content else {"status": "spam sent", "uid": uid}
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to send spam: {str(e)}"}

    @staticmethod
    def send_likes(uid: str):
        """Send likes to a specific UID"""
        try:
            params = {"uid": uid, "key": "ch9ayfa-6"}
            response = requests.get(LIKES_API, params=params, timeout=10)
            response.raise_for_status()
            return response.json() if response.content else {"status": "likes sent", "uid": uid}
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to send likes: {str(e)}"}

def format_player_info(data):
    """Format player info with stylish design"""
    try:
        message_parts = []

        # Format Basic Info
        if "basicinfo" in data and data["basicinfo"]:
            basic = data["basicinfo"][0]  # Get first item from basicinfo array

            # Header with decorative elements
            message_parts.append("╔═══════════════════════════╗")
            message_parts.append("║        👤 PLAYER INFO        ║")
            message_parts.append("╚═══════════════════════════╝")
            message_parts.append("")

            # Player Identity Section
            message_parts.append("🎮 **IDENTITY**")
            message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            message_parts.append(f"▸ **Name:** `{basic.get('username', 'N/A')}`")
            message_parts.append(f"▸ **Level:** `{basic.get('level', 'N/A')}`")
            message_parts.append(f"▸ **Region:** `{basic.get('region', 'N/A')}`")
            message_parts.append(f"▸ **OB Season:** `{basic.get('OB', 'N/A')}`")
            message_parts.append("")

            # Stats Section
            message_parts.append("📊 **STATISTICS**")
            message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            exp_formatted = f"{basic.get('Exp', 0):,}" if basic.get('Exp') else 'N/A'
            likes_formatted = f"{basic.get('likes', 0):,}" if basic.get('likes') else 'N/A'
            message_parts.append(f"▸ **Experience:** `{exp_formatted} XP`")
            message_parts.append(f"▸ **Badge Count:** `{basic.get('BadgeCount', 'N/A')} 🏆`")
            message_parts.append(f"▸ **Likes Received:** `{likes_formatted} ❤️`")
            message_parts.append("")

            # Ranking Section
            message_parts.append("🏆 **RANKING**")
            message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            message_parts.append(f"▸ **BR Score:** `{basic.get('brrankscore', 'N/A')}` | **Points:** `{basic.get('brrankpoint', 'N/A')}`")
            message_parts.append(f"▸ **CS Score:** `{basic.get('csrankscore', 'N/A')}` | **Points:** `{basic.get('csrankpoint', 'N/A')}`")
            message_parts.append("")

            # Bio Section
            if basic.get('bio') and basic.get('bio') != 'N/A':
                message_parts.append("💭 **BIO**")
                message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                message_parts.append(f"▸ *\"{basic.get('bio')}\"*")
                message_parts.append("")

            # Timestamps Section
            message_parts.append("⏰ **ACTIVITY**")
            message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

            if basic.get('createat'):
                from datetime import datetime
                create_date = datetime.fromtimestamp(basic['createat']).strftime('%d/%m/%Y at %H:%M')
                message_parts.append(f"▸ **Joined:** `{create_date}`")

            if basic.get('lastlogin'):
                from datetime import datetime
                login_date = datetime.fromtimestamp(basic['lastlogin']).strftime('%d/%m/%Y at %H:%M')
                message_parts.append(f"▸ **Last Seen:** `{login_date}`")

        # Format Clan Info
        if "claninfo" in data and data["claninfo"]:
            clan = data["claninfo"][0]  # Get first item from claninfo array
            message_parts.append("")
            message_parts.append("╔═══════════════════════════╗")
            message_parts.append("║        🏰 CLAN INFO         ║")
            message_parts.append("╚═══════════════════════════╝")
            message_parts.append("")

            if clan.get('clanid', 0) == 0 or not clan.get('clanname'):
                message_parts.append("🚫 **STATUS**")
                message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                message_parts.append("▸ **No Clan** - *Player is flying solo* 🦅")
            else:
                message_parts.append("🏷️ **CLAN DETAILS**")
                message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                message_parts.append(f"▸ **Name:** `{clan.get('clanname', 'N/A')}`")
                message_parts.append(f"▸ **ID:** `{clan.get('clanid', 'N/A')}`")
                message_parts.append(f"▸ **Guild Level:** `{clan.get('guildlevel', 'N/A')}`")
                message_parts.append(f"▸ **Active Members:** `{clan.get('livemember', 'N/A')} 👥`")

        # Footer
        message_parts.append("")
        message_parts.append("═══════════════════════════════")
        message_parts.append("🔥 Powered by 𝓓𝓔𝓥 𝓢𝓞𝓤𝓗𝓐𝓘𝓛/ch9ayfa Bot 🔥")
        message_parts.append("═══════════════════════════════")

        return "\n".join(message_parts)

    except Exception as e:
        return f"❌ Error formatting player info: {str(e)}\n\nRaw data:\n```json\n{json.dumps(data, indent=2)}\n```"

# Bot command handlers
@bot.message_handler(commands=['start'])
def send_welcome(message):
    # Check authorization first
    if not is_developer_authorized(message):
        send_unauthorized_message(message)
        return

    user_id = message.from_user.id

    # Save user info to Firebase
    user_data = {
        'username': message.from_user.username,
        'first_name': message.from_user.first_name,
        'last_name': message.from_user.last_name
    }
    save_user_info(user_id, user_data)

    # Log command usage
    log_command_usage(user_id, '/start')

    welcome_text = f"""
╔═══════════════════════════════╗
║     {get_text(user_id, 'welcome_title')}      ║
╚═══════════════════════════════╝

{get_text(user_id, 'welcome_msg')}

┌─────────────────────────────────┐
│           {get_text(user_id, 'commands_title')}           │
└─────────────────────────────────┘

🔍 `/info <uid>` - {get_text(user_id, 'player_info')}
✅ `/check <uid>` - {get_text(user_id, 'status_check')}
📅 `/events [region]` - {get_text(user_id, 'game_events')}
📧 `/spam <uid>` - {get_text(user_id, 'spam_service')}
❤️ `/likes <uid>` - {get_text(user_id, 'likes_service')}
🌐 `/language` - {get_text(user_id, 'language_service')}
❓ `/help` - Detailed help

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{get_text(user_id, 'quick_start')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    bot.reply_to(message, welcome_text, parse_mode='Markdown')

@bot.message_handler(commands=['help'])
def send_help(message):
    user_id = message.from_user.id

    # Log command usage
    log_command_usage(user_id, '/help')

    help_text = f"""
╔═══════════════════════════════╗
║        � HELP CENTER         ║
╚═══════════════════════════════╝

{get_text(user_id, 'detailed_guide')}

┌─────────────────────────────────┐
│        {get_text(user_id, 'player_info')}           │
└─────────────────────────────────┘
**Command:** `/info <uid>`
**Description:** Get detailed player statistics
**Example:** `/info 2511293320`

┌─────────────────────────────────┐
│        {get_text(user_id, 'status_check')}          │
└─────────────────────────────────┘
**Command:** `/check <uid>`
**Description:** Verify player status
**Example:** `/check 123456`

┌─────────────────────────────────┐
│        {get_text(user_id, 'game_events')}           │
└─────────────────────────────────┘
**Command:** `/events [region]`
**Description:** Get regional events
**Examples:**
  ▸ `/events` (default: ME)
  ▸ `/events US`
  ▸ `/events EU`

┌─────────────────────────────────┐
│        {get_text(user_id, 'spam_service')}          │
└─────────────────────────────────┘
**Command:** `/spam <uid>`
**Description:** Send spam to a player
**Example:** `/spam 2511293320`

┌─────────────────────────────────┐
│        ❤️ LIKES SERVICE         │
└─────────────────────────────────┘
**Command:** `/likes <uid>`
**Description:** Send likes to a player
**Example:** `/likes 2511293320`

┌─────────────────────────────────┐
│        {get_text(user_id, 'language_service')}         │
└─────────────────────────────────┘
**Command:** `/language`
**Description:** Change bot language
**Languages:** Français, English, العربية

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{get_text(user_id, 'need_help')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    bot.reply_to(message, help_text, parse_mode='Markdown')

@bot.message_handler(commands=['info'])
def get_info(message):
    user_id = message.from_user.id

    # Extract UID from command
    command_parts = message.text.split()
    if len(command_parts) < 2:
        log_command_usage(user_id, '/info', success=False)
        bot.reply_to(message, "❌ Please provide a UID. Usage: /info <uid>")
        return

    uid = command_parts[1]

    # Log command usage
    log_command_usage(user_id, '/info')
    bot.reply_to(message, f"🔄 Getting information for UID: {uid}...")

    result = APIClient.get_info(uid)

    if "error" in result:
        bot.reply_to(message, f"❌ Error: {result['error']}")
    else:
        # Format and display only basicinfo and claninfo
        formatted_message = format_player_info(result)
        bot.reply_to(message, formatted_message, parse_mode='Markdown')

@bot.message_handler(commands=['check'])
def check_status(message):
    # Extract UID from command
    command_parts = message.text.split()
    if len(command_parts) < 2:
        bot.reply_to(message, "❌ Please provide a UID. Usage: /check <uid>")
        return
    
    uid = command_parts[1]
    bot.reply_to(message, f"🔄 Checking status for UID: {uid}...")
    
    result = APIClient.check_status(uid)
    
    if "error" in result:
        bot.reply_to(message, f"❌ Error: {result['error']}")
    else:
        formatted_result = json.dumps(result, indent=2)
        bot.reply_to(message, f"✅ Status Check Result:\n```json\n{formatted_result}\n```", parse_mode='Markdown')

@bot.message_handler(commands=['events'])
def get_events(message):
    # Check authorization first
    if not is_developer_authorized(message):
        send_unauthorized_message(message)
        return

    user_id = message.from_user.id
    command_parts = message.text.split()
    region = command_parts[1] if len(command_parts) > 1 else "ME"

    # Save user info and log command
    user_data = {
        'username': message.from_user.username,
        'first_name': message.from_user.first_name,
        'last_name': message.from_user.last_name,
        'language_code': message.from_user.language_code
    }
    save_user_info(user_id, user_data)
    log_command_usage(user_id, f"/events {region}")

    bot.reply_to(message, get_text(user_id, 'events_loading'))

    result = APIClient.get_events(region)

    if "error" in result:
        bot.reply_to(message, f"{get_text(user_id, 'events_error')}: {result['error']}")
    elif result.get('status') == 'success' and 'events' in result:
        events = result['events']
        count = result.get('count', len(events))

        if count == 0:
            bot.reply_to(message, get_text(user_id, 'no_events'))
            return

        # Send header message
        header_msg = get_text(user_id, 'events_found').format(count=count)
        bot.reply_to(message, f"📅 **{region.upper()} Region**\n{header_msg}")

        # Send each event as a photo with caption
        for i, event in enumerate(events[:10], 1):  # Limit to 10 events
            try:
                image_url = event.get('image_url', '')
                title = event.get('title', f'Event {i}')

                if image_url:
                    caption = f"🎮 **Event {i}/{count}**\n📝 {title}"
                    bot.send_photo(
                        message.chat.id,
                        image_url,
                        caption=caption,
                        parse_mode='Markdown'
                    )
                else:
                    bot.send_message(
                        message.chat.id,
                        f"🎮 **Event {i}/{count}**\n📝 {title}\n❌ No image available"
                    )
            except Exception as e:
                logger.error(f"Error sending event image: {e}")
                bot.send_message(
                    message.chat.id,
                    f"🎮 **Event {i}/{count}**\n📝 {title}\n❌ Image loading failed"
                )
    else:
        # Fallback to JSON format if structure is different
        formatted_result = json.dumps(result, indent=2)
        bot.reply_to(message, f"📅 Events Result:\n```json\n{formatted_result}\n```", parse_mode='Markdown')

@bot.message_handler(commands=['spam'])
def send_spam(message):
    # Extract UID from command
    command_parts = message.text.split()
    if len(command_parts) < 2:
        bot.reply_to(message, "❌ Please provide a UID. Usage: /spam <uid>")
        return

    uid = command_parts[1]

    # Stylish loading message
    loading_message = f"""
╔═══════════════════════════════╗
║        📧 SENDING SPAM         ║
╚═══════════════════════════════╝

🎯 **Target UID:** `{uid}`
🔄 **Status:** Processing...
⏳ **Please wait...**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    bot.reply_to(message, loading_message, parse_mode='Markdown')

    result = APIClient.send_spam(uid)

    if "error" in result:
        error_message = f"""
╔═══════════════════════════════╗
║        ❌ SPAM FAILED          ║
╚═══════════════════════════════╝

🎯 **Target UID:** `{uid}`
❌ **Error:** {result['error']}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔄 **Try again later or check the UID**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        bot.reply_to(message, error_message, parse_mode='Markdown')
    else:
        success_message = f"""
╔═══════════════════════════════╗
║        ✅ SPAM SENT!           ║
╚═══════════════════════════════╝

🎯 **Target UID:** `{uid}`
✅ **Status:** Success!
📧 **Spam delivered successfully!**

📊 **Response Details:**
```json
{json.dumps(result, indent=2)}
```

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔥 **Mission accomplished!** 🔥
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        bot.reply_to(message, success_message, parse_mode='Markdown')

@bot.message_handler(commands=['likes'])
def send_likes(message):
    # Extract UID from command
    command_parts = message.text.split()
    if len(command_parts) < 2:
        bot.reply_to(message, "❌ Please provide a UID. Usage: /likes <uid>")
        return

    uid = command_parts[1]

    # Stylish loading message
    loading_message = f"""
╔═══════════════════════════════╗
║        ❤️ SENDING LIKES        ║
╚═══════════════════════════════╝

🎯 **Target UID:** `{uid}`
🔄 **Status:** Processing...
⏳ **Please wait...**

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    bot.reply_to(message, loading_message, parse_mode='Markdown')

    result = APIClient.send_likes(uid)

    if "error" in result:
        error_message = f"""
╔═══════════════════════════════╗
║        ❌ LIKES FAILED         ║
╚═══════════════════════════════╝

🎯 **Target UID:** `{uid}`
❌ **Error:** {result['error']}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔄 **Try again later or check the UID**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        bot.reply_to(message, error_message, parse_mode='Markdown')
    else:
        success_message = f"""
╔═══════════════════════════════╗
║        ✅ LIKES SENT!          ║
╚═══════════════════════════════╝

🎯 **Target UID:** `{uid}`
✅ **Status:** Success!
❤️ **Likes delivered successfully!**

📊 **Response Details:**
```json
{json.dumps(result, indent=2)}
```

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔥 **Mission accomplished!** 🔥
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        bot.reply_to(message, success_message, parse_mode='Markdown')

@bot.message_handler(commands=['language'])
def select_language(message):
    user_id = message.from_user.id

    # Log command usage
    log_command_usage(user_id, '/language')

    # Create inline keyboard with language options
    markup = types.InlineKeyboardMarkup(row_width=1)

    # Language buttons
    btn_french = types.InlineKeyboardButton("🇫🇷 Français", callback_data="lang_fr")
    btn_english = types.InlineKeyboardButton("🇬🇧 English", callback_data="lang_en")
    btn_arabic = types.InlineKeyboardButton("🇸🇦 العربية", callback_data="lang_ar")

    markup.add(btn_french, btn_english, btn_arabic)

    language_text = f"""
╔═══════════════════════════════╗
║        🌐 LANGUAGE SELECTION   ║
╚═══════════════════════════════╝

{get_text(user_id, 'select_language')}

🇫🇷 **Français** - French
🇬🇧 **English** - English
🇸🇦 **العربية** - Arabic

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💡 **Choose your preferred language**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """

    bot.reply_to(message, language_text, parse_mode='Markdown', reply_markup=markup)

@bot.callback_query_handler(func=lambda call: call.data.startswith('lang_'))
def handle_language_selection(call):
    user_id = call.from_user.id
    lang_code = call.data.split('_')[1]

    # Update user's language preference in Firebase
    firebase_db.set_user_language(user_id, lang_code)

    # Language confirmation messages
    confirmations = {
        'fr': '✅ **Langue changée en Français !**\n🔥 Tapez `/help` pour voir les commandes en français.',
        'en': '✅ **Language changed to English!**\n🔥 Type `/help` to see commands in English.',
        'ar': '✅ **تم تغيير اللغة إلى العربية!**\n🔥 اكتب `/help` لرؤية الأوامر بالعربية.'
    }

    confirmation_text = f"""
╔═══════════════════════════════╗
║        ✅ LANGUAGE UPDATED     ║
╚═══════════════════════════════╝

{confirmations.get(lang_code, confirmations['en'])}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🌐 **Language successfully changed!**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """

    # Edit the message to show confirmation
    bot.edit_message_text(
        chat_id=call.message.chat.id,
        message_id=call.message.message_id,
        text=confirmation_text,
        parse_mode='Markdown'
    )

    # Answer the callback query
    bot.answer_callback_query(call.id, f"Language changed to {lang_code.upper()}!")

# Handle unknown commands
@bot.message_handler(func=lambda message: True)
def handle_unknown(message):
    user_id = message.from_user.id

    unknown_text = f"""
╔═══════════════════════════════╗
║        {get_text(user_id, 'unknown_command')}        ║
╚═══════════════════════════════╝

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{get_text(user_id, 'dont_recognize')}

{get_text(user_id, 'available_commands')}
▸ `/start` - Welcome message
▸ `/help` - Detailed help
▸ `/info <uid>` - Player info
▸ `/check <uid>` - Status check
▸ `/events [region]` - Game events
▸ `/spam <uid>` - Spam service
▸ `/likes <uid>` - Send likes
▸ `/language` - Change language

{get_text(user_id, 'type_help')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    """
    bot.reply_to(message, unknown_text, parse_mode='Markdown')

if __name__ == "__main__":
    print("🤖 Starting Telegram Bot...")
    print(f"Bot token configured: {'✅' if BOT_TOKEN else '❌'}")
    print(f"API key: {API_KEY}")
    
    try:
        bot.infinity_polling()
    except Exception as e:
        print(f"❌ Bot error: {e}")
