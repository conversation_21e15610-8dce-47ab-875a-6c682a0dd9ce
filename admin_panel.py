import os
import json
from datetime import datetime, timedelta
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from dotenv import load_dotenv
from firebase_db import get_firebase_manager
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app configuration
app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key-change-this')

# Flask-Login configuration
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
login_manager.login_message_category = 'info'

# Firebase manager
firebase_db = get_firebase_manager()

# Admin user configuration
ADMIN_USER_IDS = [
    int(id.strip()) for id in os.getenv('ADMIN_USER_IDS', '').split(',') if id.strip()
]

# Simple admin credentials (in production, use a proper database)
ADMIN_CREDENTIALS = {
    'admin': generate_password_hash('admin123'),  # Change this password!
    'souhail': generate_password_hash('ch9ayfa2024')  # Change this password!
}

class User(UserMixin):
    def __init__(self, username):
        self.id = username
        self.username = username

@login_manager.user_loader
def load_user(user_id):
    if user_id in ADMIN_CREDENTIALS:
        return User(user_id)
    return None

@app.route('/')
def index():
    """Redirect to dashboard if logged in, otherwise to login"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username in ADMIN_CREDENTIALS and check_password_hash(ADMIN_CREDENTIALS[username], password):
            user = User(username)
            login_user(user)
            flash('Connexion réussie!', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect.', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Logout admin user"""
    logout_user()
    flash('Vous avez été déconnecté.', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard page"""
    try:
        # Get basic statistics
        stats = get_dashboard_stats()
        return render_template('dashboard.html', stats=stats)
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        flash('Erreur lors du chargement du tableau de bord.', 'error')
        return render_template('dashboard.html', stats={})

@app.route('/users')
@login_required
def users():
    """Users management page"""
    try:
        # Get all users from Firebase
        users_data = firebase_db.get_all_users()
        return render_template('users.html', users=users_data)
    except Exception as e:
        logger.error(f"Error loading users: {e}")
        flash('Erreur lors du chargement des utilisateurs.', 'error')
        return render_template('users.html', users=[])

@app.route('/commands')
@login_required
def commands():
    """Commands usage statistics page"""
    try:
        # Get command usage statistics
        commands_data = firebase_db.get_command_statistics()
        return render_template('commands.html', commands=commands_data)
    except Exception as e:
        logger.error(f"Error loading commands: {e}")
        flash('Erreur lors du chargement des statistiques de commandes.', 'error')
        return render_template('commands.html', commands=[])

@app.route('/settings')
@login_required
def settings():
    """Bot settings page"""
    try:
        # Get current bot configuration
        config = {
            'bot_token': os.getenv('TELEGRAM_BOT_TOKEN', ''),
            'api_key': os.getenv('API_KEY', ''),
            'developer_group_id': os.getenv('DEVELOPER_GROUP_ID', ''),
            'admin_user_ids': os.getenv('ADMIN_USER_IDS', ''),
            'flask_port': os.getenv('FLASK_PORT', '5000'),
            'flask_debug': os.getenv('FLASK_DEBUG', 'True')
        }
        return render_template('settings.html', config=config)
    except Exception as e:
        logger.error(f"Error loading settings: {e}")
        flash('Erreur lors du chargement des paramètres.', 'error')
        return render_template('settings.html', config={})

@app.route('/api/stats')
@login_required
def api_stats():
    """API endpoint for dashboard statistics"""
    try:
        stats = get_dashboard_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        return jsonify({'error': str(e)}), 500

def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        # Get basic statistics from Firebase
        total_users = firebase_db.get_total_users()
        total_commands = firebase_db.get_total_commands()
        recent_activity = firebase_db.get_recent_activity(limit=10)
        
        # Calculate additional stats
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        stats = {
            'total_users': total_users,
            'total_commands': total_commands,
            'recent_activity': recent_activity,
            'bot_status': 'Online',  # This could be dynamic
            'admin_count': len(ADMIN_USER_IDS),
            'developer_group_id': os.getenv('DEVELOPER_GROUP_ID', 'Non configuré')
        }
        
        return stats
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        return {
            'total_users': 0,
            'total_commands': 0,
            'recent_activity': [],
            'bot_status': 'Error',
            'admin_count': len(ADMIN_USER_IDS),
            'developer_group_id': os.getenv('DEVELOPER_GROUP_ID', 'Non configuré')
        }

if __name__ == '__main__':
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"🌐 Starting Admin Panel on port {port}...")
    print(f"🔧 Debug mode: {'ON' if debug else 'OFF'}")
    print(f"👥 Admin users configured: {len(ADMIN_CREDENTIALS)}")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
