# CH9AYFA Bot System - Configuration Example
# Copiez ce fichier vers .env et remplissez vos valeurs

# =============================================================================
# CONFIGURATION TELEGRAM BOT (OBLIGATOIRE)
# =============================================================================

# Token du bot Telegram obtenu depuis @BotFather
# Exemple: 1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_BOT_TOKEN=votre_token_bot_ici

# Clé API pour les services externes
# Valeur par défaut: ch9ayfa
API_KEY=ch9ayfa

# =============================================================================
# CONFIGURATION SÉCURITÉ ET ACCÈS
# =============================================================================

# ID du groupe Telegram où le bot est autorisé à fonctionner
# Pour obtenir l'ID: ajoutez le bot au groupe et utilisez /getgroupid
# Exemple: -1001234567890
DEVELOPER_GROUP_ID=-1001234567890

# IDs des utilisateurs administrateurs (séparés par des virgules)
# Pour obtenir votre ID: envoyez un message au bot et consultez les logs
# Exemple: 123456789,987654321
ADMIN_USER_IDS=123456789,987654321

# =============================================================================
# CONFIGURATION FLASK ADMIN PANEL
# =============================================================================

# Clé secrète pour les sessions Flask (changez cette valeur!)
# Générez une clé aléatoire sécurisée
FLASK_SECRET_KEY=changez-cette-cle-secrete-maintenant

# Port sur lequel le panel d'administration sera accessible
# Valeur par défaut: 5000
FLASK_PORT=5000

# Mode debug Flask (True pour développement, False pour production)
# Valeur par défaut: True
FLASK_DEBUG=True

# =============================================================================
# CONFIGURATION FIREBASE (OPTIONNEL)
# =============================================================================

# Si vous n'utilisez pas Firebase, le système utilisera un stockage local
# Pour utiliser Firebase:
# 1. Créez un projet Firebase
# 2. Téléchargez le fichier service account JSON
# 3. Renommez-le: souhail-a101a-firebase-adminsdk-fbsvc-fc33476ebd.json
# 4. Placez-le dans le dossier racine du projet

# =============================================================================
# INSTRUCTIONS DE CONFIGURATION
# =============================================================================

# 1. CRÉER UN BOT TELEGRAM:
#    - Ouvrez Telegram et cherchez @BotFather
#    - Envoyez /newbot et suivez les instructions
#    - Copiez le token et collez-le dans TELEGRAM_BOT_TOKEN

# 2. OBTENIR L'ID DU GROUPE:
#    - Ajoutez votre bot au groupe de développeurs
#    - Assurez-vous que le bot est administrateur du groupe
#    - Utilisez la commande /getgroupid (réservée aux admins)
#    - Copiez l'ID affiché dans DEVELOPER_GROUP_ID

# 3. OBTENIR VOTRE ID UTILISATEUR:
#    - Envoyez un message privé à votre bot
#    - Consultez les logs du bot pour voir votre ID
#    - Ajoutez votre ID dans ADMIN_USER_IDS

# 4. SÉCURISER LE SYSTÈME:
#    - Changez FLASK_SECRET_KEY par une valeur aléatoire
#    - En production, définissez FLASK_DEBUG=False
#    - Utilisez HTTPS en production
#    - Configurez un reverse proxy (nginx recommandé)

# =============================================================================
# EXEMPLE DE CONFIGURATION COMPLÈTE
# =============================================================================

# TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
# API_KEY=ch9ayfa
# DEVELOPER_GROUP_ID=-1001234567890
# ADMIN_USER_IDS=123456789,987654321
# FLASK_SECRET_KEY=ma-cle-secrete-super-securisee-2024
# FLASK_PORT=5000
# FLASK_DEBUG=True

# =============================================================================
# NOTES IMPORTANTES
# =============================================================================

# - Ne partagez jamais votre fichier .env
# - Ajoutez .env à votre .gitignore
# - Sauvegardez vos configurations en lieu sûr
# - Testez votre configuration avec: python test_system.py
# - Démarrez le système avec: python start.py

# =============================================================================
# SUPPORT
# =============================================================================

# En cas de problème:
# 1. Vérifiez que toutes les variables sont définies
# 2. Testez avec: python test_system.py
# 3. Consultez les logs d'erreur
# 4. Contactez l'équipe de développement
