#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CH9AYFA Bot System v2.0 - Configuration Settings
Advanced configuration management with environment validation
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TelegramConfig:
    """Telegram bot configuration"""
    bot_token: str
    api_key: str = "ch9ayfa"
    developer_group_id: Optional[str] = None
    admin_user_ids: List[int] = field(default_factory=list)
    webhook_url: Optional[str] = None
    webhook_secret: Optional[str] = None
    
    def __post_init__(self):
        if not self.bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN is required")
        
        # Parse admin user IDs
        if isinstance(self.admin_user_ids, str):
            self.admin_user_ids = [
                int(id.strip()) for id in self.admin_user_ids.split(',') if id.strip()
            ]

@dataclass
class FlaskConfig:
    """Flask application configuration"""
    secret_key: str
    port: int = 5000
    host: str = "0.0.0.0"
    debug: bool = True
    ssl_context: Optional[str] = None
    
    def __post_init__(self):
        if not self.secret_key:
            raise ValueError("FLASK_SECRET_KEY is required")

@dataclass
class DatabaseConfig:
    """Database configuration"""
    firebase_credentials_path: str = "souhail-a101a-firebase-adminsdk-fbsvc-fc33476ebd.json"
    use_local_fallback: bool = True
    local_db_path: str = "data/local_db.json"
    backup_enabled: bool = True
    backup_interval: int = 3600  # seconds

@dataclass
class APIConfig:
    """External API configuration"""
    info_api_base: str = "https://info-ch9ayfa.vercel.app"
    check_api: str = "https://ch9ayfa-check-1.vercel.app/check_status"
    events_api: str = "https://eventes-ch9ayfa.vercel.app/eventes"
    spam_api: str = "https://spam-ch9ayfa.vercel.app/spam"
    likes_api: str = "https://likes-ch9ayfa-v6.vercel.app/like"
    timeout: int = 10
    retry_attempts: int = 3
    retry_delay: int = 1

@dataclass
class SecurityConfig:
    """Security configuration"""
    rate_limit_enabled: bool = True
    max_requests_per_minute: int = 60
    session_timeout: int = 3600
    password_min_length: int = 8
    enable_2fa: bool = False
    allowed_ips: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        # Parse allowed IPs
        if isinstance(self.allowed_ips, str):
            self.allowed_ips = [
                ip.strip() for ip in self.allowed_ips.split(',') if ip.strip()
            ]

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    file_path: str = "logs/ch9ayfa_bot.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    enable_console: bool = True
    enable_file: bool = True

@dataclass
class FeatureFlags:
    """Feature flags for enabling/disabling features"""
    enable_info_command: bool = True
    enable_events_command: bool = True
    enable_spam_command: bool = True
    enable_likes_command: bool = True
    enable_language_command: bool = True
    enable_admin_commands: bool = True
    enable_analytics: bool = True
    enable_notifications: bool = True
    enable_multi_bot: bool = False

class Settings:
    """Main settings class that combines all configurations"""
    
    def __init__(self):
        self.telegram = self._load_telegram_config()
        self.flask = self._load_flask_config()
        self.database = self._load_database_config()
        self.api = self._load_api_config()
        self.security = self._load_security_config()
        self.logging = self._load_logging_config()
        self.features = self._load_feature_flags()
        
        # Validate configuration
        self._validate_config()
        
        logger.info("✅ Configuration loaded successfully")
    
    def _load_telegram_config(self) -> TelegramConfig:
        """Load Telegram configuration from environment"""
        return TelegramConfig(
            bot_token=os.getenv('TELEGRAM_BOT_TOKEN', ''),
            api_key=os.getenv('API_KEY', 'ch9ayfa'),
            developer_group_id=os.getenv('DEVELOPER_GROUP_ID'),
            admin_user_ids=os.getenv('ADMIN_USER_IDS', ''),
            webhook_url=os.getenv('WEBHOOK_URL'),
            webhook_secret=os.getenv('WEBHOOK_SECRET')
        )
    
    def _load_flask_config(self) -> FlaskConfig:
        """Load Flask configuration from environment"""
        return FlaskConfig(
            secret_key=os.getenv('FLASK_SECRET_KEY', ''),
            port=int(os.getenv('FLASK_PORT', '5000')),
            host=os.getenv('FLASK_HOST', '0.0.0.0'),
            debug=os.getenv('FLASK_DEBUG', 'True').lower() == 'true',
            ssl_context=os.getenv('FLASK_SSL_CONTEXT')
        )
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration from environment"""
        return DatabaseConfig(
            firebase_credentials_path=os.getenv('FIREBASE_CREDENTIALS_PATH', 
                                               'souhail-a101a-firebase-adminsdk-fbsvc-fc33476ebd.json'),
            use_local_fallback=os.getenv('USE_LOCAL_FALLBACK', 'True').lower() == 'true',
            local_db_path=os.getenv('LOCAL_DB_PATH', 'data/local_db.json'),
            backup_enabled=os.getenv('BACKUP_ENABLED', 'True').lower() == 'true',
            backup_interval=int(os.getenv('BACKUP_INTERVAL', '3600'))
        )
    
    def _load_api_config(self) -> APIConfig:
        """Load API configuration from environment"""
        return APIConfig(
            info_api_base=os.getenv('INFO_API_BASE', 'https://info-ch9ayfa.vercel.app'),
            check_api=os.getenv('CHECK_API', 'https://ch9ayfa-check-1.vercel.app/check_status'),
            events_api=os.getenv('EVENTS_API', 'https://eventes-ch9ayfa.vercel.app/eventes'),
            spam_api=os.getenv('SPAM_API', 'https://spam-ch9ayfa.vercel.app/spam'),
            likes_api=os.getenv('LIKES_API', 'https://likes-ch9ayfa-v6.vercel.app/like'),
            timeout=int(os.getenv('API_TIMEOUT', '10')),
            retry_attempts=int(os.getenv('API_RETRY_ATTEMPTS', '3')),
            retry_delay=int(os.getenv('API_RETRY_DELAY', '1'))
        )
    
    def _load_security_config(self) -> SecurityConfig:
        """Load security configuration from environment"""
        return SecurityConfig(
            rate_limit_enabled=os.getenv('RATE_LIMIT_ENABLED', 'True').lower() == 'true',
            max_requests_per_minute=int(os.getenv('MAX_REQUESTS_PER_MINUTE', '60')),
            session_timeout=int(os.getenv('SESSION_TIMEOUT', '3600')),
            password_min_length=int(os.getenv('PASSWORD_MIN_LENGTH', '8')),
            enable_2fa=os.getenv('ENABLE_2FA', 'False').lower() == 'true',
            allowed_ips=os.getenv('ALLOWED_IPS', '')
        )
    
    def _load_logging_config(self) -> LoggingConfig:
        """Load logging configuration from environment"""
        return LoggingConfig(
            level=os.getenv('LOG_LEVEL', 'INFO'),
            file_path=os.getenv('LOG_FILE_PATH', 'logs/ch9ayfa_bot.log'),
            max_file_size=int(os.getenv('LOG_MAX_FILE_SIZE', str(10 * 1024 * 1024))),
            backup_count=int(os.getenv('LOG_BACKUP_COUNT', '5')),
            format=os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            enable_console=os.getenv('LOG_ENABLE_CONSOLE', 'True').lower() == 'true',
            enable_file=os.getenv('LOG_ENABLE_FILE', 'True').lower() == 'true'
        )
    
    def _load_feature_flags(self) -> FeatureFlags:
        """Load feature flags from environment"""
        return FeatureFlags(
            enable_info_command=os.getenv('ENABLE_INFO_COMMAND', 'True').lower() == 'true',
            enable_events_command=os.getenv('ENABLE_EVENTS_COMMAND', 'True').lower() == 'true',
            enable_spam_command=os.getenv('ENABLE_SPAM_COMMAND', 'True').lower() == 'true',
            enable_likes_command=os.getenv('ENABLE_LIKES_COMMAND', 'True').lower() == 'true',
            enable_language_command=os.getenv('ENABLE_LANGUAGE_COMMAND', 'True').lower() == 'true',
            enable_admin_commands=os.getenv('ENABLE_ADMIN_COMMANDS', 'True').lower() == 'true',
            enable_analytics=os.getenv('ENABLE_ANALYTICS', 'True').lower() == 'true',
            enable_notifications=os.getenv('ENABLE_NOTIFICATIONS', 'True').lower() == 'true',
            enable_multi_bot=os.getenv('ENABLE_MULTI_BOT', 'False').lower() == 'true'
        )
    
    def _validate_config(self):
        """Validate the configuration"""
        errors = []
        
        # Validate required fields
        if not self.telegram.bot_token:
            errors.append("TELEGRAM_BOT_TOKEN is required")
        
        if not self.flask.secret_key:
            errors.append("FLASK_SECRET_KEY is required")
        
        # Validate port range
        if not (1 <= self.flask.port <= 65535):
            errors.append("FLASK_PORT must be between 1 and 65535")
        
        # Validate logging level
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.logging.level.upper() not in valid_levels:
            errors.append(f"LOG_LEVEL must be one of: {', '.join(valid_levels)}")
        
        if errors:
            raise ValueError(f"Configuration errors: {'; '.join(errors)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary"""
        return {
            'telegram': self.telegram.__dict__,
            'flask': self.flask.__dict__,
            'database': self.database.__dict__,
            'api': self.api.__dict__,
            'security': self.security.__dict__,
            'logging': self.logging.__dict__,
            'features': self.features.__dict__
        }
    
    def save_to_file(self, file_path: str):
        """Save configuration to JSON file"""
        config_dict = self.to_dict()
        # Remove sensitive information
        config_dict['telegram']['bot_token'] = '***HIDDEN***'
        config_dict['flask']['secret_key'] = '***HIDDEN***'
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, default=str)
        
        logger.info(f"Configuration saved to {file_path}")

# Global settings instance
settings = None

def get_settings() -> Settings:
    """Get global settings instance"""
    global settings
    if settings is None:
        settings = Settings()
    return settings

def reload_settings():
    """Reload settings from environment"""
    global settings
    settings = Settings()
    logger.info("Settings reloaded")

if __name__ == "__main__":
    # Test configuration loading
    try:
        config = Settings()
        print("✅ Configuration loaded successfully!")
        print(f"Bot token configured: {'✅' if config.telegram.bot_token else '❌'}")
        print(f"Flask port: {config.flask.port}")
        print(f"Debug mode: {config.flask.debug}")
        print(f"Admin users: {len(config.telegram.admin_user_ids)}")
        
        # Save configuration example
        config.save_to_file('config_example.json')
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
