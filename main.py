#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CH9AYFA Bot Main Launcher
Launches both Telegram Bot and Flask Admin Panel
"""

import os
import sys
import threading
import time
import signal
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def run_telegram_bot():
    """Run the Telegram bot in a separate thread"""
    try:
        logger.info("🤖 Starting Telegram Bot...")
        
        # Import and run the bot
        import telegram_bot
        
        # The bot will run indefinitely
        logger.info("✅ Telegram Bot started successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to start Telegram Bot: {e}")
        import traceback
        traceback.print_exc()

def run_flask_app():
    """Run the Flask admin panel in a separate thread"""
    try:
        logger.info("🌐 Starting Flask Admin Panel...")
        
        # Import and run Flask app
        from admin_panel import app
        
        port = int(os.getenv('FLASK_PORT', 5000))
        debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
        
        # Run Flask app
        app.run(host='0.0.0.0', port=port, debug=debug, use_reloader=False)
        
        logger.info("✅ Flask Admin Panel started successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to start Flask Admin Panel: {e}")
        import traceback
        traceback.print_exc()

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info("🛑 Received shutdown signal. Stopping services...")
    sys.exit(0)

def main():
    """Main function to start both services"""
    
    # Print banner
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║                    🔥 CH9AYFA BOT SYSTEM 🔥                   ║
║                                                               ║
║                   Telegram Bot + Admin Panel                 ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
    """)
    
    # Check required environment variables
    required_vars = ['TELEGRAM_BOT_TOKEN']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file and ensure all required variables are set.")
        sys.exit(1)
    
    # Display configuration
    logger.info("📋 Configuration:")
    logger.info(f"   • Bot Token: {'✅ Set' if os.getenv('TELEGRAM_BOT_TOKEN') else '❌ Missing'}")
    logger.info(f"   • API Key: {os.getenv('API_KEY', 'ch9ayfa')}")
    logger.info(f"   • Developer Group ID: {os.getenv('DEVELOPER_GROUP_ID', 'Not set')}")
    logger.info(f"   • Admin User IDs: {os.getenv('ADMIN_USER_IDS', 'Not set')}")
    logger.info(f"   • Flask Port: {os.getenv('FLASK_PORT', '5000')}")
    logger.info(f"   • Flask Debug: {os.getenv('FLASK_DEBUG', 'True')}")
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Create threads for both services
        telegram_thread = threading.Thread(target=run_telegram_bot, daemon=True)
        flask_thread = threading.Thread(target=run_flask_app, daemon=True)
        
        # Start both services
        logger.info("🚀 Starting services...")
        
        # Start Telegram bot
        telegram_thread.start()
        time.sleep(2)  # Give bot time to start
        
        # Start Flask app
        flask_thread.start()
        time.sleep(2)  # Give Flask time to start
        
        logger.info("✅ All services started successfully!")
        logger.info("📱 Telegram Bot: Running")
        logger.info(f"🌐 Admin Panel: http://localhost:{os.getenv('FLASK_PORT', '5000')}")
        logger.info("🔧 Use Ctrl+C to stop all services")
        
        # Keep main thread alive
        while True:
            time.sleep(1)
            
            # Check if threads are still alive
            if not telegram_thread.is_alive():
                logger.warning("⚠️ Telegram Bot thread died, restarting...")
                telegram_thread = threading.Thread(target=run_telegram_bot, daemon=True)
                telegram_thread.start()
            
            if not flask_thread.is_alive():
                logger.warning("⚠️ Flask thread died, restarting...")
                flask_thread = threading.Thread(target=run_flask_app, daemon=True)
                flask_thread.start()
    
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt. Shutting down...")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        logger.info("👋 CH9AYFA Bot System stopped.")

if __name__ == "__main__":
    main()
