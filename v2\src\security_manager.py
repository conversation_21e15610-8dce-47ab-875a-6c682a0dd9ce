#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CH9AYFA Bot System v2.0 - Security Manager
Enhanced security features with rate limiting, IP filtering, and threat detection
"""

import time
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
import ipaddress

logger = logging.getLogger(__name__)

@dataclass
class SecurityEvent:
    """Security event data structure"""
    event_type: str
    user_id: Optional[int] = None
    ip_address: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)
    severity: str = 'low'  # low, medium, high, critical

@dataclass
class RateLimitEntry:
    """Rate limit tracking entry"""
    requests: deque = field(default_factory=deque)
    last_request: datetime = field(default_factory=datetime.now)
    blocked_until: Optional[datetime] = None

class SecurityManager:
    """Enhanced security manager with multiple protection layers"""
    
    def __init__(self, security_config):
        self.config = security_config
        
        # Rate limiting
        self.rate_limits: Dict[int, RateLimitEntry] = defaultdict(RateLimitEntry)
        self.global_rate_limit: RateLimitEntry = RateLimitEntry()
        
        # IP filtering
        self.allowed_ips: Set[str] = set()
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Dict[str, int] = defaultdict(int)
        
        # Security events
        self.security_events: deque = deque(maxlen=1000)  # Keep last 1000 events
        
        # Threat detection
        self.failed_attempts: Dict[int, List[datetime]] = defaultdict(list)
        self.suspicious_patterns: Dict[str, int] = defaultdict(int)
        
        # Session management
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Initialize allowed IPs
        self._initialize_allowed_ips()
        
        logger.info("✅ Security Manager initialized")
    
    def _initialize_allowed_ips(self):
        """Initialize allowed IP addresses"""
        if self.config.allowed_ips:
            for ip in self.config.allowed_ips:
                try:
                    # Validate IP address
                    ipaddress.ip_address(ip)
                    self.allowed_ips.add(ip)
                    logger.debug(f"Added allowed IP: {ip}")
                except ValueError:
                    logger.warning(f"Invalid IP address in allowed list: {ip}")
    
    def check_rate_limit(self, user_id: int, action: str = 'command') -> bool:
        """Check if user is within rate limits"""
        if not self.config.rate_limit_enabled:
            return True
        
        now = datetime.now()
        user_limit = self.rate_limits[user_id]
        
        # Clean old requests (older than 1 minute)
        minute_ago = now - timedelta(minutes=1)
        while user_limit.requests and user_limit.requests[0] < minute_ago:
            user_limit.requests.popleft()
        
        # Check if user is temporarily blocked
        if user_limit.blocked_until and now < user_limit.blocked_until:
            self._log_security_event(
                'rate_limit_blocked',
                user_id=user_id,
                details={'action': action, 'blocked_until': user_limit.blocked_until.isoformat()},
                severity='medium'
            )
            return False
        
        # Check rate limit
        if len(user_limit.requests) >= self.config.max_requests_per_minute:
            # Block user for escalating time based on violations
            violation_count = self.suspicious_patterns[f"rate_limit_{user_id}"]
            block_duration = min(300, 60 * (2 ** violation_count))  # Max 5 minutes
            
            user_limit.blocked_until = now + timedelta(seconds=block_duration)
            self.suspicious_patterns[f"rate_limit_{user_id}"] += 1
            
            self._log_security_event(
                'rate_limit_exceeded',
                user_id=user_id,
                details={
                    'action': action,
                    'requests_count': len(user_limit.requests),
                    'block_duration': block_duration,
                    'violation_count': violation_count
                },
                severity='high' if violation_count > 2 else 'medium'
            )
            
            logger.warning(f"Rate limit exceeded for user {user_id}, blocked for {block_duration}s")
            return False
        
        # Add current request
        user_limit.requests.append(now)
        user_limit.last_request = now
        
        return True
    
    def check_ip_allowed(self, ip_address: str) -> bool:
        """Check if IP address is allowed"""
        if not self.config.allowed_ips:  # If no IP restrictions configured
            return True
        
        if ip_address in self.blocked_ips:
            self._log_security_event(
                'blocked_ip_access',
                ip_address=ip_address,
                details={'reason': 'IP in blocked list'},
                severity='high'
            )
            return False
        
        if self.allowed_ips and ip_address not in self.allowed_ips:
            self.suspicious_ips[ip_address] += 1
            
            self._log_security_event(
                'unauthorized_ip_access',
                ip_address=ip_address,
                details={'attempt_count': self.suspicious_ips[ip_address]},
                severity='medium'
            )
            
            # Auto-block IP after multiple attempts
            if self.suspicious_ips[ip_address] >= 5:
                self.blocked_ips.add(ip_address)
                logger.warning(f"Auto-blocked IP {ip_address} after {self.suspicious_ips[ip_address]} attempts")
            
            return False
        
        return True
    
    def validate_user_input(self, user_input: str, input_type: str = 'general') -> bool:
        """Validate user input for security threats"""
        if not user_input:
            return True
        
        # Check for common injection patterns
        dangerous_patterns = [
            r'<script',
            r'javascript:',
            r'onload=',
            r'onerror=',
            r'eval\(',
            r'exec\(',
            r'system\(',
            r'shell_exec',
            r'passthru',
            r'file_get_contents',
            r'file_put_contents',
            r'fopen',
            r'fwrite',
            r'include',
            r'require',
            r'../../../',
            r'..\\..\\',
            r'union\s+select',
            r'drop\s+table',
            r'delete\s+from',
            r'insert\s+into',
            r'update\s+set'
        ]
        
        user_input_lower = user_input.lower()
        
        for pattern in dangerous_patterns:
            if pattern in user_input_lower:
                self._log_security_event(
                    'malicious_input_detected',
                    details={
                        'input_type': input_type,
                        'pattern': pattern,
                        'input_length': len(user_input)
                    },
                    severity='high'
                )
                logger.warning(f"Malicious input detected: {pattern}")
                return False
        
        # Check input length
        max_lengths = {
            'command': 1000,
            'uid': 20,
            'message': 4000,
            'general': 2000
        }
        
        max_length = max_lengths.get(input_type, max_lengths['general'])
        if len(user_input) > max_length:
            self._log_security_event(
                'input_too_long',
                details={
                    'input_type': input_type,
                    'input_length': len(user_input),
                    'max_length': max_length
                },
                severity='medium'
            )
            return False
        
        return True
    
    def create_session(self, user_id: int, additional_data: Dict[str, Any] = None) -> str:
        """Create a secure session"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            'user_id': user_id,
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'expires_at': datetime.now() + timedelta(seconds=self.config.session_timeout),
            'ip_address': additional_data.get('ip_address') if additional_data else None,
            'user_agent': additional_data.get('user_agent') if additional_data else None
        }
        
        if additional_data:
            session_data.update(additional_data)
        
        self.active_sessions[session_id] = session_data
        
        self._log_security_event(
            'session_created',
            user_id=user_id,
            details={'session_id': session_id[:8] + '...'},
            severity='low'
        )
        
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Validate and refresh session"""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        now = datetime.now()
        
        # Check if session expired
        if now > session['expires_at']:
            del self.active_sessions[session_id]
            self._log_security_event(
                'session_expired',
                user_id=session['user_id'],
                details={'session_id': session_id[:8] + '...'},
                severity='low'
            )
            return None
        
        # Update last activity and extend expiration
        session['last_activity'] = now
        session['expires_at'] = now + timedelta(seconds=self.config.session_timeout)
        
        return session
    
    def destroy_session(self, session_id: str) -> bool:
        """Destroy a session"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            del self.active_sessions[session_id]
            
            self._log_security_event(
                'session_destroyed',
                user_id=session['user_id'],
                details={'session_id': session_id[:8] + '...'},
                severity='low'
            )
            
            return True
        
        return False
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        now = datetime.now()
        expired_sessions = [
            session_id for session_id, session in self.active_sessions.items()
            if now > session['expires_at']
        ]
        
        for session_id in expired_sessions:
            self.destroy_session(session_id)
        
        if expired_sessions:
            logger.debug(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def detect_suspicious_activity(self, user_id: int, activity_type: str, details: Dict[str, Any] = None) -> bool:
        """Detect suspicious activity patterns"""
        now = datetime.now()
        
        # Track activity patterns
        pattern_key = f"{activity_type}_{user_id}"
        self.suspicious_patterns[pattern_key] += 1
        
        # Define thresholds for different activities
        thresholds = {
            'failed_login': 5,
            'invalid_command': 10,
            'api_error': 15,
            'rate_limit_hit': 3,
            'malicious_input': 1
        }
        
        threshold = thresholds.get(activity_type, 10)
        
        if self.suspicious_patterns[pattern_key] >= threshold:
            self._log_security_event(
                'suspicious_activity_detected',
                user_id=user_id,
                details={
                    'activity_type': activity_type,
                    'count': self.suspicious_patterns[pattern_key],
                    'threshold': threshold,
                    'additional_details': details or {}
                },
                severity='high'
            )
            
            logger.warning(f"Suspicious activity detected for user {user_id}: {activity_type} (count: {self.suspicious_patterns[pattern_key]})")
            return True
        
        return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate a secure random token"""
        return secrets.token_urlsafe(length)
    
    def hash_sensitive_data(self, data: str, salt: str = None) -> str:
        """Hash sensitive data with salt"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        combined = f"{data}{salt}"
        hashed = hashlib.sha256(combined.encode()).hexdigest()
        
        return f"{salt}:{hashed}"
    
    def verify_hashed_data(self, data: str, hashed_data: str) -> bool:
        """Verify hashed data"""
        try:
            salt, expected_hash = hashed_data.split(':', 1)
            combined = f"{data}{salt}"
            actual_hash = hashlib.sha256(combined.encode()).hexdigest()
            
            return actual_hash == expected_hash
        except ValueError:
            return False
    
    def _log_security_event(self, event_type: str, user_id: int = None, ip_address: str = None, 
                           details: Dict[str, Any] = None, severity: str = 'low'):
        """Log security event"""
        event = SecurityEvent(
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            details=details or {},
            severity=severity
        )
        
        self.security_events.append(event)
        
        # Log to system logger based on severity
        log_message = f"Security event: {event_type}"
        if user_id:
            log_message += f" (user: {user_id})"
        if ip_address:
            log_message += f" (IP: {ip_address})"
        if details:
            log_message += f" - {details}"
        
        if severity == 'critical':
            logger.critical(log_message)
        elif severity == 'high':
            logger.error(log_message)
        elif severity == 'medium':
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def get_security_stats(self) -> Dict[str, Any]:
        """Get security statistics"""
        now = datetime.now()
        
        # Count events by type and severity
        event_types = defaultdict(int)
        severity_counts = defaultdict(int)
        recent_events = 0
        
        hour_ago = now - timedelta(hours=1)
        
        for event in self.security_events:
            event_types[event.event_type] += 1
            severity_counts[event.severity] += 1
            
            if event.timestamp >= hour_ago:
                recent_events += 1
        
        return {
            'total_events': len(self.security_events),
            'recent_events_1h': recent_events,
            'event_types': dict(event_types),
            'severity_counts': dict(severity_counts),
            'active_sessions': len(self.active_sessions),
            'blocked_ips': len(self.blocked_ips),
            'suspicious_ips': len(self.suspicious_ips),
            'rate_limited_users': len([
                user_id for user_id, limit in self.rate_limits.items()
                if limit.blocked_until and now < limit.blocked_until
            ])
        }
    
    def get_recent_security_events(self, limit: int = 50, severity_filter: str = None) -> List[SecurityEvent]:
        """Get recent security events"""
        events = list(self.security_events)
        
        # Filter by severity if specified
        if severity_filter:
            events = [event for event in events if event.severity == severity_filter]
        
        # Sort by timestamp (newest first) and limit
        events.sort(key=lambda x: x.timestamp, reverse=True)
        return events[:limit]
    
    def block_ip(self, ip_address: str, reason: str = "Manual block"):
        """Manually block an IP address"""
        self.blocked_ips.add(ip_address)
        
        self._log_security_event(
            'ip_blocked',
            ip_address=ip_address,
            details={'reason': reason},
            severity='medium'
        )
        
        logger.info(f"IP {ip_address} blocked: {reason}")
    
    def unblock_ip(self, ip_address: str):
        """Unblock an IP address"""
        if ip_address in self.blocked_ips:
            self.blocked_ips.remove(ip_address)
            
            self._log_security_event(
                'ip_unblocked',
                ip_address=ip_address,
                severity='low'
            )
            
            logger.info(f"IP {ip_address} unblocked")
    
    def reset_user_violations(self, user_id: int):
        """Reset security violations for a user"""
        # Clear rate limit data
        if user_id in self.rate_limits:
            del self.rate_limits[user_id]
        
        # Clear suspicious patterns
        patterns_to_remove = [key for key in self.suspicious_patterns.keys() if key.endswith(f"_{user_id}")]
        for pattern in patterns_to_remove:
            del self.suspicious_patterns[pattern]
        
        # Clear failed attempts
        if user_id in self.failed_attempts:
            del self.failed_attempts[user_id]
        
        self._log_security_event(
            'user_violations_reset',
            user_id=user_id,
            severity='low'
        )
        
        logger.info(f"Security violations reset for user {user_id}")

# Global security manager instance
security_manager_instance = None

def get_security_manager(security_config=None) -> SecurityManager:
    """Get global security manager instance"""
    global security_manager_instance
    if security_manager_instance is None:
        if security_config is None:
            from config.settings import get_settings
            settings = get_settings()
            security_config = settings.security
        security_manager_instance = SecurityManager(security_config)
    return security_manager_instance

if __name__ == "__main__":
    # Test security manager
    from config.settings import get_settings
    
    settings = get_settings()
    sm = SecurityManager(settings.security)
    
    print("Testing Security Manager...")
    
    # Test rate limiting
    user_id = 12345
    for i in range(65):  # Exceed rate limit
        allowed = sm.check_rate_limit(user_id)
        if not allowed:
            print(f"Rate limit hit at request {i+1}")
            break
    
    # Test input validation
    malicious_inputs = [
        "<script>alert('xss')</script>",
        "'; DROP TABLE users; --",
        "../../../etc/passwd",
        "eval(malicious_code)"
    ]
    
    for input_text in malicious_inputs:
        is_safe = sm.validate_user_input(input_text)
        print(f"Input '{input_text[:20]}...': {'SAFE' if is_safe else 'BLOCKED'}")
    
    # Test session management
    session_id = sm.create_session(user_id, {'ip_address': '***********'})
    print(f"Created session: {session_id[:8]}...")
    
    session = sm.validate_session(session_id)
    print(f"Session valid: {session is not None}")
    
    # Get stats
    stats = sm.get_security_stats()
    print(f"Security stats: {stats}")
    
    print("\n✅ Security Manager test completed!")
