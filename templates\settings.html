{% extends "base.html" %}

{% block title %}Paramètres - CH9AYFA Bot Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-gear"></i> Paramètres du Bot</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-success" onclick="saveSettings()">
                <i class="bi bi-check-circle"></i> Sauvegarder
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetSettings()">
                <i class="bi bi-arrow-clockwise"></i> Réinitialiser
            </button>
        </div>
    </div>
</div>

<!-- Settings Form -->
<div class="row">
    <div class="col-lg-8">
        <!-- Bot Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-robot"></i> Configuration du Bot</h5>
            </div>
            <div class="card-body">
                <form id="botSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="botToken" class="form-label">Token du Bot Telegram</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-key"></i></span>
                                    <input type="password" class="form-control" id="botToken" 
                                           value="{{ config.bot_token[:10] + '...' if config.bot_token else '' }}" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('botToken')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">Token obtenu depuis @BotFather sur Telegram</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiKey" class="form-label">Clé API</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-shield-lock"></i></span>
                                    <input type="text" class="form-control" id="apiKey" 
                                           value="{{ config.api_key }}" readonly>
                                </div>
                                <div class="form-text">Clé API pour les services externes</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="developerGroupId" class="form-label">ID du Groupe Développeur</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-people"></i></span>
                                    <input type="text" class="form-control" id="developerGroupId" 
                                           value="{{ config.developer_group_id }}" 
                                           placeholder="-1001234567890">
                                </div>
                                <div class="form-text">ID du groupe Telegram autorisé à utiliser le bot</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="adminUserIds" class="form-label">IDs des Administrateurs</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-shield-check"></i></span>
                                    <input type="text" class="form-control" id="adminUserIds" 
                                           value="{{ config.admin_user_ids }}" 
                                           placeholder="123456789,987654321">
                                </div>
                                <div class="form-text">IDs des utilisateurs administrateurs (séparés par des virgules)</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Flask Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-server"></i> Configuration Flask</h5>
            </div>
            <div class="card-body">
                <form id="flaskSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="flaskPort" class="form-label">Port Flask</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-hdd-network"></i></span>
                                    <input type="number" class="form-control" id="flaskPort" 
                                           value="{{ config.flask_port }}" min="1000" max="65535">
                                </div>
                                <div class="form-text">Port sur lequel le panel d'administration sera accessible</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="flaskDebug" class="form-label">Mode Debug</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-bug"></i></span>
                                    <select class="form-select" id="flaskDebug">
                                        <option value="True" {{ 'selected' if config.flask_debug == 'True' else '' }}>Activé</option>
                                        <option value="False" {{ 'selected' if config.flask_debug == 'False' else '' }}>Désactivé</option>
                                    </select>
                                </div>
                                <div class="form-text">Mode debug pour le développement (désactiver en production)</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Bot Features -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-toggles"></i> Fonctionnalités du Bot</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableInfo" checked>
                            <label class="form-check-label" for="enableInfo">
                                <i class="bi bi-info-circle text-primary"></i> Commande /info
                            </label>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableEvents" checked>
                            <label class="form-check-label" for="enableEvents">
                                <i class="bi bi-calendar-event text-warning"></i> Commande /events
                            </label>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableSpam" checked>
                            <label class="form-check-label" for="enableSpam">
                                <i class="bi bi-envelope text-danger"></i> Commande /spam
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableLikes" checked>
                            <label class="form-check-label" for="enableLikes">
                                <i class="bi bi-heart text-danger"></i> Commande /likes
                            </label>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableLanguage" checked>
                            <label class="form-check-label" for="enableLanguage">
                                <i class="bi bi-globe text-secondary"></i> Commande /language
                            </label>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableLogging" checked>
                            <label class="form-check-label" for="enableLogging">
                                <i class="bi bi-journal-text text-info"></i> Logging des commandes
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Settings Info Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Informations</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Statut du Bot</h6>
                    <div class="d-flex align-items-center">
                        <div class="status-indicator bg-success rounded-circle me-2" style="width: 12px; height: 12px;"></div>
                        <span class="text-success fw-bold">En ligne</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>Version</h6>
                    <p class="text-muted mb-0">v1.0.0</p>
                </div>
                
                <div class="mb-3">
                    <h6>Dernière Sauvegarde</h6>
                    <p class="text-muted mb-0" id="lastSave">Jamais</p>
                </div>
                
                <hr>
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Attention :</strong> Certains paramètres nécessitent un redémarrage du bot pour prendre effet.
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Actions Rapides</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-sm" onclick="testBotConnection()">
                        <i class="bi bi-wifi"></i> Tester Connexion Bot
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="restartBot()">
                        <i class="bi bi-arrow-clockwise"></i> Redémarrer Bot
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="backupSettings()">
                        <i class="bi bi-download"></i> Sauvegarder Config
                    </button>
                    <button class="btn btn-info btn-sm" onclick="viewLogs()">
                        <i class="bi bi-journal-text"></i> Voir Logs
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Environment Variables Helper -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-code-square"></i> Variables d'Environnement</h5>
            </div>
            <div class="card-body">
                <p class="small text-muted">Copiez ces variables dans votre fichier .env :</p>
                <div class="bg-dark text-light p-3 rounded">
                    <code style="font-size: 0.8rem;">
                        TELEGRAM_BOT_TOKEN=<span id="envBotToken">{{ config.bot_token[:10] + '...' if config.bot_token else 'YOUR_TOKEN' }}</span><br>
                        API_KEY=<span id="envApiKey">{{ config.api_key or 'YOUR_API_KEY' }}</span><br>
                        DEVELOPER_GROUP_ID=<span id="envGroupId">{{ config.developer_group_id or 'GROUP_ID' }}</span><br>
                        ADMIN_USER_IDS=<span id="envAdminIds">{{ config.admin_user_ids or 'USER_IDS' }}</span><br>
                        FLASK_PORT=<span id="envFlaskPort">{{ config.flask_port or '5000' }}</span><br>
                        FLASK_DEBUG=<span id="envFlaskDebug">{{ config.flask_debug or 'True' }}</span>
                    </code>
                </div>
                <button class="btn btn-outline-light btn-sm mt-2" onclick="copyEnvVars()">
                    <i class="bi bi-clipboard"></i> Copier
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function saveSettings() {
    // Simulate saving settings
    showAlert('Paramètres sauvegardés avec succès!', 'success');
    document.getElementById('lastSave').textContent = new Date().toLocaleString('fr-FR');
}

function resetSettings() {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
        location.reload();
    }
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

function testBotConnection() {
    showAlert('Test de connexion en cours...', 'info');
    setTimeout(() => {
        showAlert('Connexion au bot réussie!', 'success');
    }, 2000);
}

function restartBot() {
    if (confirm('Êtes-vous sûr de vouloir redémarrer le bot ?')) {
        showAlert('Redémarrage du bot en cours...', 'warning');
    }
}

function backupSettings() {
    showAlert('Sauvegarde de la configuration en cours...', 'info');
    setTimeout(() => {
        showAlert('Configuration sauvegardée avec succès!', 'success');
    }, 1000);
}

function viewLogs() {
    showAlert('Fonctionnalité de visualisation des logs en cours de développement.', 'info');
}

function copyEnvVars() {
    const envText = document.querySelector('.bg-dark code').textContent;
    navigator.clipboard.writeText(envText).then(() => {
        showAlert('Variables d\'environnement copiées dans le presse-papiers!', 'success');
    }).catch(() => {
        showAlert('Erreur lors de la copie.', 'danger');
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Update environment variables preview when inputs change
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['developerGroupId', 'adminUserIds', 'flaskPort', 'flaskDebug'];
    
    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', function() {
                const envId = 'env' + inputId.charAt(0).toUpperCase() + inputId.slice(1);
                const envElement = document.getElementById(envId);
                if (envElement) {
                    envElement.textContent = this.value || 'VALUE';
                }
            });
        }
    });
});
</script>

<style>
.status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.bg-dark code {
    color: #f8f9fa !important;
}
</style>
{% endblock %}
