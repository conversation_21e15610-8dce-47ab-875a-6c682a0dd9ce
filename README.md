# Telegram Bot with API Integration

This Telegram bot integrates with multiple APIs to provide various services through chat commands.

## Features

- **Info API**: Get information from the info endpoint
- **Status Check**: Check status for specific UIDs
- **Events**: Retrieve events by region
- **Spam Service**: Send spam with configurable IDs
- **Likes Service**: Send likes to specific players
- **Multi-Language Support**: Available in French, English, and Arabic
- **Interactive Language Selection**: Easy language switching with inline buttons

## Setup Instructions

### 1. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a chat and send `/newbot`
3. Follow the instructions to create your bot
4. Copy the bot token provided by BotFather

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

1. Copy the `.env` file and add your bot token:
```
TELEGRAM_BOT_TOKEN=your_actual_bot_token_here
API_KEY=ch9ayfa
```

### 4. Run the Bot

```bash
python telegram_bot.py
```

## Bot Commands

- `/start` - Welcome message and command overview
- `/help` - Show detailed help information
- `/info <uid>` - Get information from the info API
- `/check <uid>` - Check status for a specific UID
- `/events [region]` - Get events (default region: ME)
- `/spam <uid>` - Send spam to a specific player
- `/likes <uid>` - Send likes to a specific player
- `/language` - Change bot language (French, English, Arabic)

## API Endpoints Used

1. **Info API**: `https://info-ch9ayfa.vercel.app/{uid}`
2. **Check API**: `https://ch9ayfa-check-1.vercel.app/check_status?key=ch9ayfa&uid={uid}`
3. **Events API**: `https://eventes-ch9ayfa.vercel.app/eventes?region=ME&key=ch9ayfa`
4. **Spam API**: `https://spam-ch9ayfa.vercel.app/spam?id={uid}`
5. **Likes API**: `https://likes-ch9ayfa-v6.vercel.app/like?uid={uid}&key=ch9ayfa-6`

## Usage Examples

```
/info 2511293320
/check 123456
/events US
/events ME
/spam 2511293320
/likes 2511293320
/language
```

## Error Handling

The bot includes comprehensive error handling for:
- Network timeouts
- API errors
- Invalid parameters
- Missing environment variables

## Requirements

- Python 3.7+
- Internet connection
- Valid Telegram bot token
