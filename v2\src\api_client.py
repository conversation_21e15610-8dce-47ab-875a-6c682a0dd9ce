#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CH9AYFA Bot System v2.0 - API Client
Enhanced API client with async support, retry logic, and caching
"""

import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging
import json

logger = logging.getLogger(__name__)

@dataclass
class APIResponse:
    """API response wrapper"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    response_time: float = 0.0

class APICache:
    """Simple in-memory cache for API responses"""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default TTL
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached value"""
        if key in self.cache:
            entry = self.cache[key]
            if datetime.now() < entry['expires']:
                logger.debug(f"Cache hit for key: {key}")
                return entry['data']
            else:
                # Expired, remove from cache
                del self.cache[key]
                logger.debug(f"Cache expired for key: {key}")
        
        logger.debug(f"Cache miss for key: {key}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set cached value"""
        ttl = ttl or self.default_ttl
        expires = datetime.now() + timedelta(seconds=ttl)
        
        self.cache[key] = {
            'data': value,
            'expires': expires,
            'created': datetime.now()
        }
        
        logger.debug(f"Cached value for key: {key} (TTL: {ttl}s)")
    
    def clear(self) -> None:
        """Clear all cached values"""
        self.cache.clear()
        logger.debug("Cache cleared")
    
    def cleanup_expired(self) -> None:
        """Remove expired entries"""
        now = datetime.now()
        expired_keys = [
            key for key, entry in self.cache.items()
            if now >= entry['expires']
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

class APIClient:
    """Enhanced API client with async support and caching"""
    
    def __init__(self, api_config):
        self.config = api_config
        self.cache = APICache()
        self.session = None
        
        # API endpoints
        self.endpoints = {
            'info': api_config.info_api_base,
            'check': api_config.check_api,
            'events': api_config.events_api,
            'spam': api_config.spam_api,
            'likes': api_config.likes_api
        }
        
        logger.info("✅ API Client initialized")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'User-Agent': 'CH9AYFA-Bot-v2.0',
                    'Accept': 'application/json'
                }
            )
        return self.session
    
    async def _make_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        cache_key: Optional[str] = None,
        cache_ttl: int = 300
    ) -> APIResponse:
        """Make HTTP request with retry logic and caching"""
        
        # Check cache first
        if cache_key and method.upper() == 'GET':
            cached_response = self.cache.get(cache_key)
            if cached_response:
                return APIResponse(
                    success=True,
                    data=cached_response,
                    response_time=0.0
                )
        
        start_time = time.time()
        last_error = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                session = await self._get_session()
                
                async with session.request(
                    method,
                    url,
                    params=params,
                    json=data if method.upper() != 'GET' else None
                ) as response:
                    
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        try:
                            response_data = await response.json()
                        except:
                            response_data = await response.text()
                        
                        # Cache successful GET responses
                        if cache_key and method.upper() == 'GET':
                            self.cache.set(cache_key, response_data, cache_ttl)
                        
                        logger.debug(f"API request successful: {method} {url} ({response_time:.2f}s)")
                        
                        return APIResponse(
                            success=True,
                            data=response_data,
                            status_code=response.status,
                            response_time=response_time
                        )
                    else:
                        error_text = await response.text()
                        logger.warning(f"API request failed: {response.status} - {error_text}")
                        
                        return APIResponse(
                            success=False,
                            error=f"HTTP {response.status}: {error_text}",
                            status_code=response.status,
                            response_time=response_time
                        )
            
            except asyncio.TimeoutError:
                last_error = "Request timeout"
                logger.warning(f"API request timeout (attempt {attempt + 1}/{self.config.retry_attempts})")
            
            except Exception as e:
                last_error = str(e)
                logger.warning(f"API request error (attempt {attempt + 1}/{self.config.retry_attempts}): {e}")
            
            # Wait before retry (except for last attempt)
            if attempt < self.config.retry_attempts - 1:
                await asyncio.sleep(self.config.retry_delay * (attempt + 1))
        
        # All attempts failed
        response_time = time.time() - start_time
        logger.error(f"API request failed after {self.config.retry_attempts} attempts: {last_error}")
        
        return APIResponse(
            success=False,
            error=last_error,
            response_time=response_time
        )
    
    async def get_player_info(self, uid: str) -> Optional[Dict[str, Any]]:
        """Get player information"""
        try:
            url = f"{self.endpoints['info']}/{uid}"
            cache_key = f"player_info_{uid}"
            
            response = await self._make_request(
                'GET',
                url,
                cache_key=cache_key,
                cache_ttl=600  # Cache for 10 minutes
            )
            
            if response.success:
                logger.info(f"Player info retrieved for UID: {uid}")
                return response.data
            else:
                logger.error(f"Failed to get player info for UID {uid}: {response.error}")
                return None
        
        except Exception as e:
            logger.error(f"Error getting player info: {e}")
            return None
    
    async def check_player_status(self, uid: str) -> Optional[Dict[str, Any]]:
        """Check player status"""
        try:
            params = {
                'key': self.config.api_key if hasattr(self.config, 'api_key') else 'ch9ayfa',
                'uid': uid
            }
            
            cache_key = f"player_status_{uid}"
            
            response = await self._make_request(
                'GET',
                self.endpoints['check'],
                params=params,
                cache_key=cache_key,
                cache_ttl=300  # Cache for 5 minutes
            )
            
            if response.success:
                logger.info(f"Player status checked for UID: {uid}")
                return response.data
            else:
                logger.error(f"Failed to check player status for UID {uid}: {response.error}")
                return None
        
        except Exception as e:
            logger.error(f"Error checking player status: {e}")
            return None
    
    async def get_events(self, region: str = 'ME') -> Optional[List[Dict[str, Any]]]:
        """Get game events"""
        try:
            params = {
                'region': region,
                'key': self.config.api_key if hasattr(self.config, 'api_key') else 'ch9ayfa'
            }
            
            cache_key = f"events_{region}"
            
            response = await self._make_request(
                'GET',
                self.endpoints['events'],
                params=params,
                cache_key=cache_key,
                cache_ttl=1800  # Cache for 30 minutes
            )
            
            if response.success:
                logger.info(f"Events retrieved for region: {region}")
                return response.data if isinstance(response.data, list) else [response.data]
            else:
                logger.error(f"Failed to get events for region {region}: {response.error}")
                return None
        
        except Exception as e:
            logger.error(f"Error getting events: {e}")
            return None
    
    async def send_spam(self, uid: str) -> Optional[Dict[str, Any]]:
        """Send spam to player"""
        try:
            params = {
                'id': uid
            }
            
            response = await self._make_request(
                'GET',
                self.endpoints['spam'],
                params=params
            )
            
            if response.success:
                logger.info(f"Spam sent to UID: {uid}")
                return {'success': True, 'data': response.data}
            else:
                logger.error(f"Failed to send spam to UID {uid}: {response.error}")
                return {'success': False, 'error': response.error}
        
        except Exception as e:
            logger.error(f"Error sending spam: {e}")
            return {'success': False, 'error': str(e)}
    
    async def send_likes(self, uid: str) -> Optional[Dict[str, Any]]:
        """Send likes to player"""
        try:
            params = {
                'uid': uid,
                'key': 'ch9ayfa-6'  # Special key for likes API
            }
            
            response = await self._make_request(
                'GET',
                self.endpoints['likes'],
                params=params
            )
            
            if response.success:
                logger.info(f"Likes sent to UID: {uid}")
                return {'success': True, 'data': response.data}
            else:
                logger.error(f"Failed to send likes to UID {uid}: {response.error}")
                return {'success': False, 'error': response.error}
        
        except Exception as e:
            logger.error(f"Error sending likes: {e}")
            return {'success': False, 'error': str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """Check API endpoints health"""
        health_status = {}
        
        for name, url in self.endpoints.items():
            try:
                start_time = time.time()
                
                # Simple GET request to check if endpoint is reachable
                response = await self._make_request('GET', url)
                
                health_status[name] = {
                    'status': 'healthy' if response.success else 'unhealthy',
                    'response_time': response.response_time,
                    'error': response.error if not response.success else None
                }
                
            except Exception as e:
                health_status[name] = {
                    'status': 'error',
                    'response_time': 0.0,
                    'error': str(e)
                }
        
        return health_status
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
            logger.debug("API client session closed")
    
    def clear_cache(self):
        """Clear API cache"""
        self.cache.clear()
        logger.info("API cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_entries = len(self.cache.cache)
        expired_count = 0
        
        now = datetime.now()
        for entry in self.cache.cache.values():
            if now >= entry['expires']:
                expired_count += 1
        
        return {
            'total_entries': total_entries,
            'expired_entries': expired_count,
            'active_entries': total_entries - expired_count
        }

# Global API client instance
api_client_instance = None

def get_api_client(api_config=None) -> APIClient:
    """Get global API client instance"""
    global api_client_instance
    if api_client_instance is None:
        if api_config is None:
            from config.settings import get_settings
            settings = get_settings()
            api_config = settings.api
        api_client_instance = APIClient(api_config)
    return api_client_instance

if __name__ == "__main__":
    # Test API client
    async def test_api():
        from config.settings import get_settings
        
        settings = get_settings()
        client = APIClient(settings.api)
        
        print("Testing API client...")
        
        # Test player info
        player_info = await client.get_player_info("**********")
        print(f"Player info: {player_info}")
        
        # Test events
        events = await client.get_events("ME")
        print(f"Events: {events}")
        
        # Test health check
        health = await client.health_check()
        print(f"Health check: {health}")
        
        # Test cache stats
        cache_stats = client.get_cache_stats()
        print(f"Cache stats: {cache_stats}")
        
        await client.close()
    
    asyncio.run(test_api())
