{% extends "base.html" %}

{% block title %}Commandes - CH9AYFA Bot Admin{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-terminal"></i> Statistiques des Commandes</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshCommands()">
                <i class="bi bi-arrow-clockwise"></i> Actualiser
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportCommands()">
                <i class="bi bi-download"></i> Exporter
            </button>
        </div>
    </div>
</div>

<!-- Command Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Total Commandes</h6>
                    <h3 class="mb-0">{{ commands|sum(attribute='count') if commands else 0 }}</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-terminal" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card success">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Commandes Uniques</h6>
                    <h3 class="mb-0">{{ commands|length if commands else 0 }}</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-list-ul" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card warning">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Aujourd'hui</h6>
                    <h3 class="mb-0" id="today-commands">0</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-calendar-day" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card info">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h6 class="text-uppercase mb-1">Taux de Succès</h6>
                    <h3 class="mb-0" id="success-rate">95%</h3>
                </div>
                <div class="flex-shrink-0">
                    <i class="bi bi-check-circle" style="font-size: 2rem; opacity: 0.7;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Commands Chart and Table -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bar-chart"></i> Utilisation des Commandes</h5>
            </div>
            <div class="card-body">
                {% if commands %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Commande</th>
                                    <th>Utilisations</th>
                                    <th>Succès</th>
                                    <th>Échecs</th>
                                    <th>Taux de Succès</th>
                                    <th>Dernière Utilisation</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for command in commands %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="command-icon me-2">
                                                {% if command.name == '/start' %}
                                                    <i class="bi bi-play-circle text-success"></i>
                                                {% elif command.name == '/info' %}
                                                    <i class="bi bi-info-circle text-primary"></i>
                                                {% elif command.name == '/help' %}
                                                    <i class="bi bi-question-circle text-info"></i>
                                                {% elif command.name == '/events' %}
                                                    <i class="bi bi-calendar-event text-warning"></i>
                                                {% elif command.name == '/spam' %}
                                                    <i class="bi bi-envelope text-danger"></i>
                                                {% elif command.name == '/likes' %}
                                                    <i class="bi bi-heart text-danger"></i>
                                                {% elif command.name == '/language' %}
                                                    <i class="bi bi-globe text-secondary"></i>
                                                {% else %}
                                                    <i class="bi bi-terminal text-muted"></i>
                                                {% endif %}
                                            </div>
                                            <code class="fw-bold">{{ command.name }}</code>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ command.count or 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ command.success_count or 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ command.error_count or 0 }}</span>
                                    </td>
                                    <td>
                                        {% set success_rate = ((command.success_count or 0) / (command.count or 1) * 100) | round(1) %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar {% if success_rate >= 90 %}bg-success{% elif success_rate >= 70 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                 role="progressbar" style="width: {{ success_rate }}%">
                                                {{ success_rate }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ command.last_used.strftime('%d/%m/%Y %H:%M') if command.last_used else 'N/A' }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-terminal" style="font-size: 4rem; color: #dee2e6;"></i>
                        <h4 class="text-muted mt-3">Aucune commande trouvée</h4>
                        <p class="text-muted">Les statistiques des commandes apparaîtront ici une fois que les utilisateurs auront interagi avec le bot.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Top Commands -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-trophy"></i> Top Commandes</h5>
            </div>
            <div class="card-body">
                {% if commands %}
                    {% for command in commands[:5] %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <div class="rank-badge me-2">
                                <span class="badge bg-primary">{{ loop.index }}</span>
                            </div>
                            <div>
                                <code class="fw-bold">{{ command.name }}</code>
                                <br>
                                <small class="text-muted">{{ command.count }} utilisations</small>
                            </div>
                        </div>
                        <div class="text-end">
                            {% set success_rate = ((command.success_count or 0) / (command.count or 1) * 100) | round(1) %}
                            <div class="text-{{ 'success' if success_rate >= 90 else 'warning' if success_rate >= 70 else 'danger' }}">
                                {{ success_rate }}%
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="bi bi-inbox" style="font-size: 2rem; color: #dee2e6;"></i>
                        <p class="text-muted mt-2">Aucune donnée</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Command Status -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-activity"></i> Statut des Commandes</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Commandes Actives</span>
                        <span class="text-success fw-bold">{{ commands|selectattr('count', 'greaterthan', 0)|list|length if commands else 0 }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Commandes Populaires</span>
                        <span class="text-primary fw-bold">{{ commands|selectattr('count', 'greaterthan', 10)|list|length if commands else 0 }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Commandes avec Erreurs</span>
                        <span class="text-danger fw-bold">{{ commands|selectattr('error_count', 'greaterthan', 0)|list|length if commands else 0 }}</span>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="analyzeCommands()">
                        <i class="bi bi-graph-up"></i> Analyser Tendances
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="optimizeCommands()">
                        <i class="bi bi-speedometer2"></i> Optimiser Performance
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshCommands() {
    location.reload();
}

function exportCommands() {
    showAlert('Fonctionnalité d\'export en cours de développement.', 'info');
}

function analyzeCommands() {
    showAlert('Analyse des tendances en cours de développement.', 'info');
}

function optimizeCommands() {
    showAlert('Optimisation de performance en cours de développement.', 'info');
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Simulate real-time updates
setInterval(() => {
    // Update today's commands count (simulate)
    const todayElement = document.getElementById('today-commands');
    if (todayElement) {
        const currentCount = parseInt(todayElement.textContent);
        todayElement.textContent = currentCount + Math.floor(Math.random() * 3);
    }
}, 30000);
</script>

<style>
.command-icon {
    font-size: 1.2rem;
}

.rank-badge .badge {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress {
    min-width: 80px;
}
</style>
{% endblock %}
